#!/usr/bin/env python3
"""
Skript pro reset WordPress databáze - smaže všechny migrované <PERSON> a obrázky.
"""

import logging
from db_connectors import get_mysql_connection
from config import TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_WP_TERMS, TBL_WP_TERM_TAXONOMY, TBL_WP_TERM_RELATIONSHIPS

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def reset_wordpress_db():
    """Resetuje WordPress databázi - smaže migrovaný obsah."""
    logging.info("Spouštím reset WordPress databáze...")
    
    conn = get_mysql_connection()
    cursor = conn.cursor()
    
    try:
        # Smazat všechny příspěvky kromě základních WordPress stránek
        logging.info("Mažu příspěvky...")
        cursor.execute(f"""
            DELETE FROM {TBL_WP_POSTS} 
            WHERE ID > 3
        """)
        deleted_posts = cursor.rowcount
        logging.info(f"Smazáno {deleted_posts} příspěvků")
        
        # Smazat metadata příspěvků
        logging.info("Mažu metadata příspěvků...")
        cursor.execute(f"""
            DELETE FROM {TBL_WP_POSTMETA} 
            WHERE post_id > 3
        """)
        deleted_meta = cursor.rowcount
        logging.info(f"Smazáno {deleted_meta} metadat")
        
        # Smazat vztahy mezi příspěvky a kategoriemi
        logging.info("Mažu vztahy příspěvků a kategorií...")
        cursor.execute(f"""
            DELETE FROM {TBL_WP_TERM_RELATIONSHIPS} 
            WHERE object_id > 3
        """)
        deleted_relationships = cursor.rowcount
        logging.info(f"Smazáno {deleted_relationships} vztahů")
        
        # Smazat kategorie (kromě základních)
        logging.info("Mažu kategorie...")
        cursor.execute(f"""
            DELETE FROM {TBL_WP_TERM_TAXONOMY} 
            WHERE term_taxonomy_id > 1
        """)
        deleted_taxonomies = cursor.rowcount
        
        cursor.execute(f"""
            DELETE FROM {TBL_WP_TERMS} 
            WHERE term_id > 1
        """)
        deleted_terms = cursor.rowcount
        logging.info(f"Smazáno {deleted_terms} kategorií")
        
        # Reset AUTO_INCREMENT
        logging.info("Resetuji AUTO_INCREMENT...")
        cursor.execute(f"ALTER TABLE {TBL_WP_POSTS} AUTO_INCREMENT = 4")
        cursor.execute(f"ALTER TABLE {TBL_WP_TERMS} AUTO_INCREMENT = 2")
        cursor.execute(f"ALTER TABLE {TBL_WP_TERM_TAXONOMY} AUTO_INCREMENT = 2")
        
        conn.commit()
        
        logging.info("=" * 50)
        logging.info("RESET DOKONČEN:")
        logging.info(f"Smazáno příspěvků: {deleted_posts}")
        logging.info(f"Smazáno metadat: {deleted_meta}")
        logging.info(f"Smazáno vztahů: {deleted_relationships}")
        logging.info(f"Smazáno kategorií: {deleted_terms}")
        logging.info("=" * 50)
        
    except Exception as e:
        logging.error(f"Chyba při resetu databáze: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    reset_wordpress_db()
