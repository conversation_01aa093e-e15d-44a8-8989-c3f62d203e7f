# Migrace SABRE do WordPress

Tento projekt obsahuje skripty pro migraci dat ze SABRE databáze (PostgreSQL) do WordPress databáze (MySQL).

## Obsah

- [<PERSON><PERSON>adavky](#po<PERSON><PERSON>vky)
- [Struktura projektu](#struktura-projektu)
- [Konfigurace](#konfigurace)
- [Spuštěn<PERSON> migrace](#spuštění-migrace)
- [Jednotli<PERSON><PERSON> kroky migrace](#jednotlivé-kroky-migrace)
- [Řešení problémů](#řešení-problémů)

## Požadavky

- Python 3.6+
- Přístup k PostgreSQL databázi SABRE
- Přístup k MySQL databázi WordPress
- Přístup k souborům obrázků ze SABRE

Potřebné Python knihovny (instalují se automaticky při spuštění):
- psycopg2-binary
- mysql-connector-python
- python-dotenv
- Pillow
- python-slugify
- beautifulsoup4

## Struktura projektu

```
migration/
├── .env                  # Konfigurační soubor pro citlivé údaje
├── config.py             # Načítání konfigurace
├── db_connectors.py      # Funkce pro připojení k databázím
├── utils.py              # Pomocné funkce
├── migrate_categories.py # Skript pro migraci kategorií
├── migrate_users.py      # Skript pro mapování/vytváření uživatelů
├── migrate_images.py     # Skript pro migraci obrázků
├── migrate_articles.py   # Skript pro migraci článků
├── run_migration.py      # Hlavní skript pro spuštění všech kroků
└── mappings/             # Adresář pro uložení mapování ID
    ├── category_map.json
    ├── user_map.json
    ├── image_map.json
    └── article_map.json
```

## Konfigurace

Před spuštěním migrace je potřeba upravit konfigurační soubor `.env` s přístupovými údaji k databázím a cestami k souborům:

```
# PostgreSQL (SABRE DB)
PG_DBNAME=dumabyt
PG_USER=detoxa
PG_PASSWORD=root
PG_HOST=localhost
PG_PORT=5432

# MySQL (WordPress DB)
MYSQL_DBNAME=dumabyt
MYSQL_USER=detoxa
MYSQL_PASSWORD=root
MYSQL_HOST=localhost
MYSQL_PORT=3306

# WordPress Cesty
WP_UPLOADS_PATH=/var/www/html/dumabyt.test/wp-content/uploads
WP_SITE_URL=http://dumabyt.test
 
# Cesty ke starým obrázkům
OLD_IMAGE_BASE_PATH=/home/<USER>/sabre/public_html_backup/public_html/obrazek

# Ostatní
DEFAULT_WP_USER_ID=1
SABRE_TABLE_PREFIX=prefix_
WP_TABLE_PREFIX=dumabyt_
```

## Spuštění migrace

Pro spuštění celé migrace najednou:

```bash
cd /home/<USER>/sabre/migration
./run_migration.py
```

Skript automaticky nainstaluje potřebné závislosti a spustí jednotlivé kroky migrace v správném pořadí.

## Jednotlivé kroky migrace

Pokud chcete spustit pouze konkrétní krok migrace:

1. **Migrace kategorií**:
   ```bash
   python migrate_categories.py
   ```

2. **Migrace uživatelů**:
   ```bash
   python migrate_users.py
   ```

3. **Migrace obrázků**:
   ```bash
   python migrate_images.py
   ```

4. **Migrace článků**:
   ```bash
   python migrate_articles.py
   ```

## Řešení problémů

### Logy

Logy jsou ukládány do adresáře `logs/` ve formátu `migration_YYYYMMDD_HHMMSS.log`. Obsahují detailní informace o průběhu migrace, včetně chyb a varování.

### Častá chybová hlášení

1. **Chyba připojení k databázi**:
   - Zkontrolujte přístupové údaje v souboru `.env`
   - Ověřte, že databázové servery běží a jsou dostupné

2. **Chyba při kopírování obrázků**:
   - Zkontrolujte cestu `OLD_IMAGE_BASE_PATH` v souboru `.env`
   - Ověřte, že cílový adresář `WP_UPLOADS_PATH` existuje a je zapisovatelný

3. **Chyba při vkládání do WordPress**:
   - Zkontrolujte, že WordPress databáze existuje a má správnou strukturu
   - Ověřte, že uživatel má dostatečná oprávnění pro zápis do databáze

### Opakování migrace

Skripty jsou navrženy tak, aby bylo možné migraci přerušit a později pokračovat. Mapování již zpracovaných položek je ukládáno do souborů JSON v adresáři `mappings/`. Pokud chcete migraci spustit znovu od začátku, smažte tyto soubory.
