import json
import os
import re
from slugify import slugify as pyslugify
from config import MAPPINGS_DIR
import logging
from datetime import datetime
import shutil
import mimetypes
from bs4 import BeautifulSoup

def load_mapping(filename):
    """Načte mapování ID ze souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logging.warning(f"Soubor mapování {filename} je poškozený nebo prázdný.")
            return {}
    return {}

def save_mapping(data, filename):
    """Uloží mapování ID do souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    try:
        # Zkontrolovat velikost dat
        data_size = len(str(data))
        if data_size > 10 * 1024 * 1024:  # 10 MB limit
            logging.warning(f"Velikost dat pro {filename} je {data_size / (1024*1024):.2f} MB, což může způsobit problémy.")
        
        with open(filepath, 'w') as f:
            json.dump(data, f)  # Odstranění indentace pro úsporu místa
        
        logging.info(f"Mapování uloženo do {filename} ({len(data)} záznamů)")
        return True
    except Exception as e:
        logging.error(f"Chyba při ukládání mapování do {filename}: {e}")
        return False

def generate_slug(text):
    """Vygeneruje URL-friendly slug."""
    if not text:
        return f"post-{datetime.now().strftime('%Y%m%d%H%M%S%f')}"  # Fallback pro prázdný titulek
    return pyslugify(text, max_length=200)  # max_length for WP posts table

def format_wp_datetime(dt_obj):
    """Formátuje datetime objekt pro WordPress (YYYY-MM-DD HH:MM:SS)."""
    if dt_obj is None:
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Zajistit, že dt_obj je datetime objekt
    if not isinstance(dt_obj, datetime):
        # Zkuste převést, pokud je to řetězec nebo jiný typ
        try:
            # Zkuste běžné formáty
            dt_obj = datetime.fromisoformat(str(dt_obj).replace('Z', '+00:00'))
        except ValueError:
            try:
                dt_obj = datetime.strptime(str(dt_obj), '%Y-%m-%d %H:%M:%S')
            except ValueError:
                logging.warning(f"Nepodařilo se převést {dt_obj} na datetime. Vracím aktuální čas.")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Formátování pro MySQL DATETIME
    return dt_obj.strftime('%Y-%m-%d %H:%M:%S')

def format_wp_datetime_gmt(dt_obj):
    """Formátuje datetime objekt pro WordPress (GMT)."""
    # Pro zjednodušení teď použijeme stejný čas jako lokální
    return format_wp_datetime(dt_obj)

def extract_author_name(author_html):
    """Extrahuje jméno autora z HTML odkazu."""
    if not author_html:
        return None
    
    try:
        soup = BeautifulSoup(author_html, 'html.parser')
        author_link = soup.find('a')
        if author_link:
            return author_link.text.strip()
        return author_html.strip()
    except Exception as e:
        logging.error(f"Chyba při extrakci jména autora: {e}")
        return None

def copy_image_file(src_path, dest_path):
    """Kopíruje soubor obrázku z původního umístění do nového."""
    try:
        # Vytvořit cílový adresář, pokud neexistuje
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        # Kopírovat soubor
        shutil.copy2(src_path, dest_path)
        logging.info(f"Soubor zkopírován: {src_path} -> {dest_path}")
        return True
    except Exception as e:
        logging.error(f"Chyba při kopírování souboru {src_path}: {e}")
        return False

def get_mime_type(file_path):
    """Zjistí MIME typ souboru."""
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type and file_path.lower().endswith(('.jpg', '.jpeg')):
        return 'image/jpeg'
    elif not mime_type and file_path.lower().endswith('.png'):
        return 'image/png'
    elif not mime_type and file_path.lower().endswith('.gif'):
        return 'image/gif'
    return mime_type or 'application/octet-stream'

def update_image_urls_in_content(content, image_map):
    """Aktualizuje URL obrázků v obsahu článku."""
    if not content or not image_map:
        return content

    import re
    import logging

    # Najít všechny obrázky v obsahu pomocí regex
    # Hledáme cesty typu: /obrazek/hash/filename.jpg nebo obrazek/hash/filename.jpg
    # Pozor: názvy souborů mohou obsahovat mezery, takže hledáme až do uvozovky
    image_pattern = r'/?obrazek/[a-f0-9]+/([^"<>]+\.(jpg|jpeg|png|gif|webp))'

    def find_image_replacement(match):
        """Najde náhradu pro obrázek v mapování"""
        full_path = match.group(0)  # Celá cesta: /obrazek/hash/filename.jpg nebo obrazek/hash/filename.jpg
        filename = match.group(1)   # Pouze název souboru: filename.jpg

        # Normalizovat cestu (odstranit úvodní /)
        normalized_path = full_path.lstrip('/')

        # 1. Zkusit přímé hledání podle celé cesty (s i bez úvodního /)
        if full_path in image_map:
            return image_map[full_path]['wp_url']
        if normalized_path in image_map:
            return image_map[normalized_path]['wp_url']

        # 2. Zkusit hledání podle názvu souboru
        if filename in image_map:
            return image_map[filename]['wp_url']

        # 3. Zkusit hledání podle částečné shody názvu souboru
        # Odstraníme prefixy a suffixy (např. "2-nordic-(2)-1050x600-hash.jpg" -> "nordic")
        base_name = filename

        # Odstranit příponu
        base_name = re.sub(r'\.[^.]+$', '', base_name)

        # Odstranit hash na konci (např. -680a1f086e7e4)
        base_name = re.sub(r'-[a-f0-9]{10,}$', '', base_name)

        # Odstranit rozměry (např. -1050x600)
        base_name = re.sub(r'-\d+x\d+$', '', base_name)

        # Odstranit číselné prefixy (např. "2-")
        base_name = re.sub(r'^\d+-', '', base_name)

        # Hledat v mapování podle částečné shody
        for map_path, info in image_map.items():
            if isinstance(info, dict) and 'wp_url' in info:
                map_filename = map_path.split('/')[-1]  # Získat název souboru z cesty
                map_base = re.sub(r'\.[^.]+$', '', map_filename)  # Odstranit příponu

                # Zkusit různé varianty shody
                if (base_name in map_base or map_base in base_name or
                    base_name.replace('-', '') == map_base.replace('-', '') or
                    base_name.replace('(', '').replace(')', '') == map_base.replace('(', '').replace(')', '')):
                    logging.info(f"Nalezena shoda pro obrázek: {filename} -> {map_filename}")
                    return info['wp_url']

        # 4. Pokud nic nenajdeme, vrátíme původní cestu
        logging.warning(f"Nenalezena náhrada pro obrázek: {normalized_path}")
        return full_path

    # Nahradit všechny obrázky v obsahu
    updated_content = re.sub(image_pattern, find_image_replacement, content, flags=re.IGNORECASE)

    # Také zkusit nahradit pomocí BeautifulSoup pro img tagy
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(updated_content, 'html.parser')
        images = soup.find_all('img')

        for img in images:
            for attr in ['src', 'data-src']:
                if img.has_attr(attr):
                    src = img[attr]
                    # Pokud src stále obsahuje "obrazek/", zkusit najít náhradu
                    if 'obrazek/' in src:
                        match = re.search(image_pattern, src, re.IGNORECASE)
                        if match:
                            replacement = find_image_replacement(match)
                            img[attr] = replacement

        updated_content = str(soup)
    except Exception as e:
        logging.error(f"Chyba při aktualizaci obrázků pomocí BeautifulSoup: {e}")

    return updated_content
