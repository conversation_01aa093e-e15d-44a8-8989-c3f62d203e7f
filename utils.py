import json
import os
import re
from slugify import slugify as pyslugify
from config import MAPPINGS_DIR
import logging
from datetime import datetime
import shutil
import mimetypes
from bs4 import BeautifulSoup

def load_mapping(filename):
    """Načte mapování ID ze souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            logging.warning(f"Soubor mapování {filename} je poškozený nebo prázdný.")
            return {}
    return {}

def save_mapping(data, filename):
    """Uloží mapování ID do souboru JSON."""
    filepath = os.path.join(MAPPINGS_DIR, filename)
    try:
        # Zkontrolovat velikost dat
        data_size = len(str(data))
        if data_size > 10 * 1024 * 1024:  # 10 MB limit
            logging.warning(f"Velikost dat pro {filename} je {data_size / (1024*1024):.2f} MB, což může způsobit problémy.")
        
        with open(filepath, 'w') as f:
            json.dump(data, f)  # Odstranění indentace pro úsporu místa
        
        logging.info(f"Mapování uloženo do {filename} ({len(data)} záznamů)")
        return True
    except Exception as e:
        logging.error(f"Chyba při ukládání mapování do {filename}: {e}")
        return False

def generate_slug(text):
    """Vygeneruje URL-friendly slug."""
    if not text:
        return f"post-{datetime.now().strftime('%Y%m%d%H%M%S%f')}"  # Fallback pro prázdný titulek
    return pyslugify(text, max_length=200)  # max_length for WP posts table

def format_wp_datetime(dt_obj):
    """Formátuje datetime objekt pro WordPress (YYYY-MM-DD HH:MM:SS)."""
    if dt_obj is None:
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Zajistit, že dt_obj je datetime objekt
    if not isinstance(dt_obj, datetime):
        # Zkuste převést, pokud je to řetězec nebo jiný typ
        try:
            # Zkuste běžné formáty
            dt_obj = datetime.fromisoformat(str(dt_obj).replace('Z', '+00:00'))
        except ValueError:
            try:
                dt_obj = datetime.strptime(str(dt_obj), '%Y-%m-%d %H:%M:%S')
            except ValueError:
                logging.warning(f"Nepodařilo se převést {dt_obj} na datetime. Vracím aktuální čas.")
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # Formátování pro MySQL DATETIME
    return dt_obj.strftime('%Y-%m-%d %H:%M:%S')

def format_wp_datetime_gmt(dt_obj):
    """Formátuje datetime objekt pro WordPress (GMT)."""
    # Pro zjednodušení teď použijeme stejný čas jako lokální
    return format_wp_datetime(dt_obj)

def extract_author_name(author_html):
    """Extrahuje jméno autora z HTML odkazu."""
    if not author_html:
        return None
    
    try:
        soup = BeautifulSoup(author_html, 'html.parser')
        author_link = soup.find('a')
        if author_link:
            return author_link.text.strip()
        return author_html.strip()
    except Exception as e:
        logging.error(f"Chyba při extrakci jména autora: {e}")
        return None

def copy_image_file(src_path, dest_path):
    """Kopíruje soubor obrázku z původního umístění do nového."""
    try:
        # Vytvořit cílový adresář, pokud neexistuje
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        # Kopírovat soubor
        shutil.copy2(src_path, dest_path)
        logging.info(f"Soubor zkopírován: {src_path} -> {dest_path}")
        return True
    except Exception as e:
        logging.error(f"Chyba při kopírování souboru {src_path}: {e}")
        return False

def get_mime_type(file_path):
    """Zjistí MIME typ souboru."""
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type and file_path.lower().endswith(('.jpg', '.jpeg')):
        return 'image/jpeg'
    elif not mime_type and file_path.lower().endswith('.png'):
        return 'image/png'
    elif not mime_type and file_path.lower().endswith('.gif'):
        return 'image/gif'
    return mime_type or 'application/octet-stream'

def update_image_urls_in_content(content, image_map):
    """Aktualizuje URL obrázků v obsahu článku."""
    if not content or not image_map:
        return content
    
    # Nejprve použijeme BeautifulSoup k identifikaci a aktualizaci obrázků v HTML
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(content, 'html.parser')
        images = soup.find_all('img')
        
        for img in images:
            # Zkontrolujeme atributy src a data-src
            for attr in ['src', 'data-src']:
                if img.has_attr(attr):
                    src = img[attr]
                    # Zkusíme najít cestu v mapování
                    for old_path, new_info in image_map.items():
                        if isinstance(new_info, dict) and 'wp_url' in new_info:
                            # Zkontrolujeme, zda src obsahuje old_path (může být součástí URL)
                            if old_path in src:
                                img[attr] = new_info['wp_url']
                                break
        
        # Konvertujeme zpět do řetězce
        content = str(soup)
    except Exception as e:
        logging.error(f"Chyba při aktualizaci obrázků pomocí BeautifulSoup: {e}")
        # Fallback na základní nahrazení
    
    # Jako záložní metodu použijeme přímé nahrazení
    for old_path, new_info in image_map.items():
        if isinstance(new_info, dict) and 'wp_url' in new_info:
            # Nahradíme všechny výskyty old_path v obsahu
            content = content.replace(old_path, new_info['wp_url'])
            
            # Zkusíme nahradit i varianty s URL encodingem a různými prefixy
            encoded_path = old_path.replace(' ', '%20')
            content = content.replace(encoded_path, new_info['wp_url'])
            
            # Zkusíme nahradit cestu s různými prefixy
            if 'obrazek/' in old_path:
                alt_path = old_path.replace('obrazek/', '')
                content = content.replace(alt_path, new_info['wp_url'])
    
    return content
