#!/usr/bin/env python3
"""
Complete Reset and Migration Script
===================================

This script performs a comprehensive database cleanup and re-migration with:
- User confirmation prompts for safety
- Complete cleanup of WordPress database and files
- Full migration pipeline execution
- Detailed validation and reporting
- Error handling and resume capability

Usage:
    python complete_reset_migration.py [--skip-cleanup] [--start-from=STEP]

Steps:
    1. cleanup - Database and file cleanup
    2. categories - Category migration
    3. users - User migration  
    4. images - Image migration
    5. articles - Article migration
    6. fix-content - Fix image URLs in content
    7. fix-featured - Optimize featured images
    8. validation - Final validation and reporting
"""

import argparse
import json
import logging
import os
import shutil
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path

import mysql.connector
import psycopg2

from config import (
    MYSQL_CONFIG, PG_CONFIG, MAPPINGS_DIR,
    TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_WP_USERS,
    TBL_WP_TERMS, TBL_WP_TERM_TAXONOMY, TBL_WP_TERM_RELATIONSHIPS
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

class MigrationManager:
    def __init__(self):
        self.start_time = None
        self.step_times = {}
        self.migration_stats = {}
        self.errors = []
        
        # Migration steps in order
        self.steps = [
            'cleanup',
            'categories', 
            'users',
            'images',
            'articles',
            'fix-content',
            'fix-featured',
            'validation'
        ]
    
    def confirm_action(self, message, default=False):
        """Ask user for confirmation with safety prompt"""
        while True:
            prompt = f"{message} ({'Y/n' if default else 'y/N'}): "
            response = input(prompt).strip().lower()
            
            if not response:
                return default
            elif response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please answer 'y' or 'n'")
    
    def log_step_start(self, step_name):
        """Log the start of a migration step"""
        logging.info(f"{'='*60}")
        logging.info(f"STARTING STEP: {step_name.upper()}")
        logging.info(f"{'='*60}")
        self.step_times[step_name] = time.time()
    
    def log_step_end(self, step_name, success=True):
        """Log the end of a migration step"""
        duration = time.time() - self.step_times[step_name]
        status = "COMPLETED" if success else "FAILED"
        logging.info(f"STEP {step_name.upper()} {status} in {duration:.2f} seconds")
        logging.info(f"{'='*60}")
        
        if not success:
            self.errors.append(f"Step {step_name} failed")
    
    def cleanup_database(self):
        """Clean up WordPress database"""
        if not self.confirm_action(
            "⚠️  WARNING: This will DELETE ALL migrated content from WordPress database!\n"
            "This includes posts, users, categories, and media. Continue?", 
            default=False
        ):
            logging.info("Database cleanup cancelled by user")
            return False
        
        try:
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()
            
            logging.info("Cleaning WordPress database...")
            
            # Delete posts and post metadata
            cursor.execute(f"DELETE FROM {TBL_WP_POSTMETA} WHERE post_id IN (SELECT ID FROM {TBL_WP_POSTS} WHERE post_type = 'post')")
            cursor.execute(f"DELETE FROM {TBL_WP_POSTS} WHERE post_type IN ('post', 'attachment')")
            
            # Delete term relationships
            cursor.execute(f"DELETE FROM {TBL_WP_TERM_RELATIONSHIPS}")
            
            # Delete terms and taxonomies (keep default categories)
            cursor.execute(f"DELETE FROM {TBL_WP_TERM_TAXONOMY} WHERE term_id > 1")
            cursor.execute(f"DELETE FROM {TBL_WP_TERMS} WHERE term_id > 1")
            
            # Delete users (keep admin user ID 1)
            # Note: usermeta table doesn't exist in our config, skip it
            cursor.execute(f"DELETE FROM {TBL_WP_USERS} WHERE ID > 1")
            
            # Reset auto-increment counters
            cursor.execute(f"ALTER TABLE {TBL_WP_POSTS} AUTO_INCREMENT = 1")
            cursor.execute(f"ALTER TABLE {TBL_WP_USERS} AUTO_INCREMENT = 2")
            cursor.execute(f"ALTER TABLE {TBL_WP_TERMS} AUTO_INCREMENT = 2")
            cursor.execute(f"ALTER TABLE {TBL_WP_TERM_TAXONOMY} AUTO_INCREMENT = 2")
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logging.info("✅ Database cleanup completed")
            return True
            
        except Exception as e:
            logging.error(f"Database cleanup failed: {e}")
            return False
    
    def cleanup_files(self):
        """Clean up mapping files and uploaded media"""
        if not self.confirm_action(
            "⚠️  WARNING: This will DELETE ALL mapping files and uploaded media!\n"
            "Continue?", 
            default=False
        ):
            logging.info("File cleanup cancelled by user")
            return False
        
        try:
            # Delete mapping files
            if os.path.exists(MAPPINGS_DIR):
                shutil.rmtree(MAPPINGS_DIR)
                logging.info(f"Deleted mapping directory: {MAPPINGS_DIR}")
            
            # Delete WordPress uploads
            uploads_dir = "wp-content/uploads"
            if os.path.exists(uploads_dir):
                shutil.rmtree(uploads_dir)
                logging.info(f"Deleted uploads directory: {uploads_dir}")
            
            # Recreate directories
            os.makedirs(MAPPINGS_DIR, exist_ok=True)
            os.makedirs(uploads_dir, exist_ok=True)
            
            logging.info("✅ File cleanup completed")
            return True
            
        except Exception as e:
            logging.error(f"File cleanup failed: {e}")
            return False
    
    def run_migration_script(self, script_name, step_name):
        """Run a migration script and capture its output"""
        try:
            logging.info(f"Running {script_name}...")
            
            # Run the script and capture output
            result = subprocess.run(
                [sys.executable, script_name],
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            if result.returncode == 0:
                logging.info(f"✅ {script_name} completed successfully")
                
                # Try to extract statistics from output
                output_lines = result.stdout.split('\n')
                stats = self.extract_stats_from_output(output_lines, step_name)
                self.migration_stats[step_name] = stats
                
                return True
            else:
                logging.error(f"❌ {script_name} failed with return code {result.returncode}")
                logging.error(f"Error output: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logging.error(f"❌ {script_name} timed out after 1 hour")
            return False
        except Exception as e:
            logging.error(f"❌ Error running {script_name}: {e}")
            return False
    
    def extract_stats_from_output(self, output_lines, step_name):
        """Extract statistics from migration script output"""
        stats = {}
        
        for line in output_lines:
            if 'zpracováno' in line.lower() or 'processed' in line.lower():
                # Try to extract numbers from lines like "Zpracováno: 1234 položek"
                import re
                numbers = re.findall(r'\d+', line)
                if numbers:
                    if 'selhalo' in line.lower() or 'failed' in line.lower():
                        stats['failed'] = int(numbers[0])
                    else:
                        stats['processed'] = int(numbers[0])
            
            elif 'úspěšnost' in line.lower() or 'success' in line.lower():
                # Extract success rate
                import re
                percentage = re.findall(r'(\d+(?:\.\d+)?)%', line)
                if percentage:
                    stats['success_rate'] = float(percentage[0])
        
        return stats
    
    def validate_migration(self):
        """Validate the migration results"""
        logging.info("Validating migration results...")
        validation_results = {}
        
        try:
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()
            
            # Count migrated content
            cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_POSTS} WHERE post_type = 'post'")
            validation_results['articles'] = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_POSTS} WHERE post_type = 'attachment'")
            validation_results['images'] = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_USERS} WHERE ID > 1")
            validation_results['users'] = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_TERMS} WHERE term_id > 1")
            validation_results['categories'] = cursor.fetchone()[0]
            
            # Check for old image URLs
            cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_POSTS} WHERE post_content LIKE '%/obrazek/%'")
            validation_results['old_image_urls'] = cursor.fetchone()[0]
            
            # Check featured images
            cursor.execute(f"""
                SELECT COUNT(*) FROM {TBL_WP_POSTMETA} 
                WHERE meta_key = '_thumbnail_id' AND meta_value IS NOT NULL
            """)
            validation_results['featured_images'] = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            self.migration_stats['validation'] = validation_results
            return True
            
        except Exception as e:
            logging.error(f"Validation failed: {e}")
            return False
    
    def generate_report(self):
        """Generate comprehensive migration report"""
        logging.info("Generating migration report...")
        
        total_time = time.time() - self.start_time
        
        report = {
            'migration_date': datetime.now().isoformat(),
            'total_duration_seconds': total_time,
            'total_duration_formatted': f"{total_time/60:.1f} minutes",
            'steps_completed': list(self.step_times.keys()),
            'step_durations': {
                step: time.time() - start_time 
                for step, start_time in self.step_times.items()
            },
            'migration_statistics': self.migration_stats,
            'errors': self.errors,
            'validation_results': self.migration_stats.get('validation', {}),
            'success': len(self.errors) == 0
        }
        
        # Save report to file
        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Print summary
        print("\n" + "="*80)
        print("MIGRATION REPORT SUMMARY")
        print("="*80)
        print(f"Total Duration: {report['total_duration_formatted']}")
        print(f"Success: {'✅ YES' if report['success'] else '❌ NO'}")
        
        if 'validation' in self.migration_stats:
            val = self.migration_stats['validation']
            print(f"\nMigrated Content:")
            print(f"  Articles: {val.get('articles', 0)}")
            print(f"  Images: {val.get('images', 0)}")
            print(f"  Users: {val.get('users', 0)}")
            print(f"  Categories: {val.get('categories', 0)}")
            print(f"  Featured Images: {val.get('featured_images', 0)}")
            print(f"  Old Image URLs Remaining: {val.get('old_image_urls', 0)}")
        
        if self.errors:
            print(f"\nErrors ({len(self.errors)}):")
            for error in self.errors:
                print(f"  ❌ {error}")
        
        print(f"\nDetailed report saved to: {report_file}")
        print("="*80)
        
        return report_file

    def run_complete_migration(self, skip_cleanup=False, start_from=None):
        """Run the complete migration pipeline"""
        self.start_time = time.time()

        # Determine which steps to run
        if start_from:
            if start_from not in self.steps:
                logging.error(f"Invalid start step: {start_from}")
                return False
            start_index = self.steps.index(start_from)
            steps_to_run = self.steps[start_index:]
        else:
            steps_to_run = self.steps.copy()
            if skip_cleanup:
                steps_to_run = [s for s in steps_to_run if s != 'cleanup']

        logging.info(f"Starting migration pipeline with steps: {steps_to_run}")

        # Execute each step
        for step in steps_to_run:
            self.log_step_start(step)
            success = False

            try:
                if step == 'cleanup':
                    success = self.run_cleanup_step()
                elif step == 'categories':
                    success = self.run_migration_script('migrate_categories.py', 'categories')
                elif step == 'users':
                    success = self.run_migration_script('migrate_users.py', 'users')
                elif step == 'images':
                    success = self.run_migration_script('migrate_images.py', 'images')
                elif step == 'articles':
                    success = self.run_migration_script('migrate_articles.py', 'articles')
                elif step == 'fix-content':
                    success = self.run_migration_script('fix_remaining_images.py', 'fix-content')
                elif step == 'fix-featured':
                    success = self.run_migration_script('fix_article_images.py', 'fix-featured')
                elif step == 'validation':
                    success = self.validate_migration()

                self.log_step_end(step, success)

                if not success:
                    if not self.confirm_action(f"Step {step} failed. Continue with next step?", default=False):
                        logging.error("Migration aborted by user")
                        break

            except KeyboardInterrupt:
                logging.warning("Migration interrupted by user")
                break
            except Exception as e:
                logging.error(f"Unexpected error in step {step}: {e}")
                self.log_step_end(step, False)
                if not self.confirm_action(f"Unexpected error in step {step}. Continue?", default=False):
                    break

        # Generate final report
        report_file = self.generate_report()
        return report_file

    def run_cleanup_step(self):
        """Run the cleanup step (database + files)"""
        db_success = self.cleanup_database()
        file_success = self.cleanup_files()
        return db_success and file_success

    def check_prerequisites(self):
        """Check if all required files and connections are available"""
        logging.info("Checking prerequisites...")

        # Check required migration scripts
        required_scripts = [
            'migrate_categories.py',
            'migrate_users.py',
            'migrate_images.py',
            'migrate_articles.py',
            'fix_remaining_images.py',
            'fix_article_images.py'
        ]

        missing_scripts = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_scripts.append(script)

        if missing_scripts:
            logging.error(f"Missing required scripts: {missing_scripts}")
            return False

        # Test database connections
        try:
            # Test PostgreSQL connection
            pg_conn = psycopg2.connect(**PG_CONFIG)
            pg_conn.close()
            logging.info("✅ PostgreSQL connection successful")

            # Test MySQL connection
            mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
            mysql_conn.close()
            logging.info("✅ MySQL connection successful")

        except Exception as e:
            logging.error(f"Database connection failed: {e}")
            return False

        logging.info("✅ All prerequisites met")
        return True


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Complete Reset and Migration Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python complete_reset_migration.py                    # Full reset and migration
  python complete_reset_migration.py --skip-cleanup    # Skip cleanup, run migration only
  python complete_reset_migration.py --start-from=images  # Start from images step
        """
    )

    parser.add_argument(
        '--skip-cleanup',
        action='store_true',
        help='Skip database and file cleanup'
    )

    parser.add_argument(
        '--start-from',
        choices=['cleanup', 'categories', 'users', 'images', 'articles', 'fix-content', 'fix-featured', 'validation'],
        help='Start migration from specific step'
    )

    parser.add_argument(
        '--yes', '-y',
        action='store_true',
        help='Answer yes to all confirmation prompts (DANGEROUS!)'
    )

    args = parser.parse_args()

    # Create migration manager
    manager = MigrationManager()

    # Override confirmation method if --yes flag is used
    if args.yes:
        manager.confirm_action = lambda msg, default=False: True
        logging.warning("⚠️ Auto-confirmation enabled - all prompts will be answered 'yes'")

    # Check prerequisites
    if not manager.check_prerequisites():
        logging.error("Prerequisites not met. Exiting.")
        sys.exit(1)

    # Show warning and get final confirmation
    if not args.skip_cleanup and not args.start_from:
        print("\n" + "⚠️ "*20)
        print("COMPLETE RESET AND MIGRATION")
        print("⚠️ "*20)
        print("This will:")
        print("1. DELETE ALL WordPress content (posts, users, categories, media)")
        print("2. DELETE ALL mapping files")
        print("3. DELETE ALL uploaded media files")
        print("4. Run complete migration from PostgreSQL")
        print("\nThis action is IRREVERSIBLE!")

        if not manager.confirm_action("Are you absolutely sure you want to continue?", default=False):
            print("Migration cancelled.")
            sys.exit(0)

    # Run migration
    try:
        report_file = manager.run_complete_migration(
            skip_cleanup=args.skip_cleanup,
            start_from=args.start_from
        )

        if report_file:
            logging.info(f"Migration completed. Report saved to: {report_file}")
            sys.exit(0)
        else:
            logging.error("Migration failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logging.warning("Migration interrupted by user")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
