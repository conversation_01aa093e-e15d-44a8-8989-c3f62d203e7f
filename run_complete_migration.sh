#!/bin/bash

# Skript pro spuštění kompletní migrace a opravy všech problémů s obrázky
# Wrapper pro migrate_and_fix_all.py

# Nastavení cesty k logovacímu adresáři
LOG_DIR="logs"
mkdir -p $LOG_DIR

# Datum a čas pro název logu
DATE=$(date +"%Y%m%d_%H%M%S")
MAIN_LOG="$LOG_DIR/run_complete_migration_$DATE.log"

# Funkce pro zobrazení nápovědy
show_help() {
  echo "==================================================================="
  echo "  KOMPLETNÍ MIGRACE A OPRAVA DAT Z POSTGRESQL DO WORDPRESS"
  echo "==================================================================="
  echo
  echo "Použití: $0 [MOŽNOSTI]"
  echo
  echo "Možnosti:"
  echo "  -h, --help          Zobrazí tuto nápovědu"
  echo "  -r, --reset         Resetuje WordPress databázi před migrací (POZOR: smaže všechna data)"
  echo "  -f, --only-fixes    Přeskočí základní migraci a provede pouze opravy"
  echo "  -i, --only-images   Provede pouze migraci a opravu obrázků"
  echo "  -d, --dry-run       Provede testovací běh bez ukládání změn"
  echo "  -y, --yes           Automaticky potvrdí všechny dotazy (neinteraktivní režim)"
  echo
  echo "Příklady použití:"
  echo "  $0                  Provede kompletní migraci a opravy"
  echo "  $0 --reset          Resetuje WordPress databázi a provede kompletní migraci"
  echo "  $0 --only-fixes     Provede pouze opravy (přeskočí základní migraci)"
  echo "  $0 --dry-run        Provede testovací běh bez ukládání změn"
  echo "==================================================================="
}

# Parsování parametrů
RESET=""
ONLY_FIXES=""
ONLY_IMAGES=""
DRY_RUN=""
AUTO_YES=""

while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -r|--reset)
      RESET="--reset"
      shift
      ;;
    -f|--only-fixes)
      ONLY_FIXES="--only-fixes"
      shift
      ;;
    -i|--only-images)
      ONLY_IMAGES="--only-images"
      shift
      ;;
    -d|--dry-run)
      DRY_RUN="--dry-run"
      shift
      ;;
    -y|--yes)
      AUTO_YES="yes"
      shift
      ;;
    *)
      echo "Neznámý parametr: $1"
      show_help
      exit 1
      ;;
  esac
done

# Kontrola Python interpreteru
if ! command -v python3 &> /dev/null; then
  echo "CHYBA: Python 3 nebyl nalezen. Nainstalujte Python 3 pro spuštění tohoto skriptu."
  exit 1
fi

# Zobrazení varování při resetu databáze
if [[ -n $RESET && -z $DRY_RUN && -z $AUTO_YES ]]; then
  echo "!!! VAROVÁNÍ !!!"
  echo "Chystáte se resetovat WordPress databázi. Všechna data budou smazána!"
  read -p "Jste si jisti, že chcete pokračovat? (y/n): " confirm
  if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo "Operace zrušena."
    exit 0
  fi
fi

# Sestavení příkazu
CMD="python3 migrate_and_fix_all.py $RESET $ONLY_FIXES $ONLY_IMAGES $DRY_RUN"

# Spuštění s logováním
echo "Spouštím kompletní migraci..."
echo "Příkaz: $CMD"
echo "Log bude uložen do: $MAIN_LOG"

# Spuštění a logování do souboru i na konzoli
$CMD 2>&1 | tee $MAIN_LOG

# Kontrola výsledku
exit_code=${PIPESTATUS[0]}
if [[ $exit_code -eq 0 ]]; then
  echo
  echo "Migrace dokončena úspěšně."
  echo "Log je uložen v: $MAIN_LOG"
else
  echo
  echo "Migrace selhala s návratovým kódem: $exit_code"
  echo "Zkontrolujte log soubor: $MAIN_LOG"
fi

exit $exit_code
