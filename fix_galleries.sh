#!/bin/bash

# Skript pro spuštění opravy galeri<PERSON>, k<PERSON><PERSON> maj<PERSON> ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obr<PERSON><PERSON>

echo "Spouštím opravu galerií s chybě<PERSON><PERSON><PERSON><PERSON><PERSON> obr<PERSON>ky..."
echo ""

# <PERSON><PERSON><PERSON><PERSON>, zda je Python nainstalován
if ! command -v python3 &> /dev/null; then
    echo "Python 3 není nainstalován. Prosím, nainstalujte Python 3 a zkuste to znovu."
    exit 1
fi

# Ko<PERSON>rola, zda existuje soubor s opravou
if [ ! -f "fix_missing_gallery_images.py" ]; then
    echo "Soubor fix_missing_gallery_images.py nebyl nalezen v aktuálním adresáři."
    exit 1
fi

# Kontrola, zda jsou nainstalované potřebné balí<PERSON>ky
python3 -c "import mysql.connector" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Bal<PERSON>ček mysql-connector-python není nainstalován."
    echo "Instaluji..."
    pip3 install mysql-connector-python
    
    # Kontrola, zda se instalace povedla
    python3 -c "import mysql.connector" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Instalace mysql-connector-python selhala. Zkuste ho nainstalovat ručně:"
        echo "pip3 install mysql-connector-python"
        exit 1
    fi
fi

# Nastavení oprávnění pro spuštění
chmod +x fix_missing_gallery_images.py

# Spuštění skriptu pro opravu
python3 fix_missing_gallery_images.py

# Kontrola, zda skript proběhl úspěšně
if [ $? -ne 0 ]; then
    echo ""
    echo "Oprava selhala. Podívejte se do logů pro více informací."
    exit 1
fi

echo ""
echo "Oprava dokončena. Podívejte se do logů pro detail informací o opravě."
echo "Log soubor: logs/fix_gallery_images_*.log"
