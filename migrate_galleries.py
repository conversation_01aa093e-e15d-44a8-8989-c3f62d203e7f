#!/usr/bin/env python3
import logging
import os
import mysql.connector
from datetime import datetime
import json
import sys
import re
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_GALERIE, TBL_GALERIE_POLOZKA, TBL_OBRAZEK,
    TBL_WP_POSTS, TBL_WP_POSTMETA,
    DEFAULT_WP_USER_ID, WP_SITE_URL,
    OLD_IMAGE_BASE_PATH
)
from utils import (
    load_mapping, save_mapping, format_wp_datetime
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_gallery_items(article_id, unique_id, article_title, cursor):
    """
    Pomocná diagnostická funkce pro výpis detailů o obrázcích ve článku.
    Pomáhá identifikovat problémy s migrací galerie.
    """
    logging.info(f"=== DIAGNOSTIKA GALERIE PRO ČLÁNEK {article_id} ({article_title}) ===")
    
    # Výpis všech obrázků
    cursor.execute(f"""
        SELECT id_obrazek, soubor, typ, priorita, active_state 
        FROM {TBL_OBRAZEK} 
        WHERE polozka_id = %s
        ORDER BY typ, priorita DESC, id_obrazek
    """, (unique_id,))
    
    all_images = cursor.fetchall()
    if not all_images:
        logging.warning(f"  Nenalezeny žádné obrázky pro unikatni_id = {unique_id}")
        return
    
    # Výpis počtu obrázků podle typu
    typu_0_count = sum(1 for img in all_images if img[2] == 0)
    typu_1_count = sum(1 for img in all_images if img[2] == 1)
    aktivni_count = sum(1 for img in all_images if img[4] == 1)
    
    logging.info(f"  Nalezeno celkem {len(all_images)} obrázků:")
    logging.info(f"  - {typu_0_count} úvodních obrázků (typ=0)")
    logging.info(f"  - {typu_1_count} obrázků pro galerii (typ=1)")
    logging.info(f"  - {aktivni_count} aktivních obrázků (active_state=1)")
    
    # Výpis detailů obrázků pro galerii (typ=1)
    logging.info("  Detaily obrázků galerie (typ=1):")
    for idx, (img_id, img_file, img_type, img_priority, img_active) in enumerate(
        [img for img in all_images if img[2] == 1], 1
    ):
        status = "AKTIVNÍ" if img_active == 1 else "NEAKTIVNÍ"
        logging.info(f"  {idx}. ID: {img_id}, Soubor: {img_file}, Priorita: {img_priority}, Stav: {status}")

def migrate_galleries(target_article_id=None):
    """
    Migruje galerie z SABRE databáze do WordPress.
    
    Proces:
    1. Načte mapování obrázků a článků
    2. Získá články a jejich unikatni_id
    3. Pro každý článek najde obrázky s odpovídajícím polozka_id
    4. Vytvoří WordPress gallery shortcode
    5. Aktualizuje obsah článku přidáním galerie
    """
    logging.info("Spouštím migraci galerií...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')
    gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
    
    # Počítadla
    new_galleries_count = 0
    failed_galleries_count = 0
    
    try:
        # 1. Získáme všechny články s jejich unikatni_id
        query = f"""
            SELECT id_clanek, unikatni_id, nazev 
            FROM {TBL_CLANEK} 
            WHERE unikatni_id IS NOT NULL AND unikatni_id != ''
        """
        
        # Pokud je zadáno konkrétní ID článku, omezíme výběr jen na něj
        if target_article_id:
            query += f" AND id_clanek = {target_article_id}"
            logging.info(f"Cílení pouze na článek ID {target_article_id}")
            
        pg_cursor.execute(query)
        articles = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles)} článků s unikatním ID.")
        
        # 2. Pro každý článek vytvoříme galerii z obrázků se stejným polozka_id
        for article_id, unique_id, article_title in articles:
            try:
                # Kontrola, zda byl článek migrován do WordPress
                if str(article_id) not in article_map:
                    logging.warning(f"Článek ID {article_id} ({article_title}) nebyl migrován, přeskakuji.")
                    continue
                
                wp_post_id = article_map[str(article_id)]
                
                # Kontrola, zda již byla pro tento článek vytvořena galerie
                gallery_key = f"article_{article_id}"
                if gallery_key in gallery_map:
                    logging.info(f"Galerie pro článek ID {article_id} již byla zpracována, přeskakuji.")
                    continue
                
                # Spustit diagnostiku pro kontrolu stavu obrázků článku
                debug_gallery_items(article_id, unique_id, article_title, pg_cursor)
                
                # 3. Získat obrázky z tabulky obrazek pro tento článek (podle polozka_id = unikatni_id)
                # Přidáváme filtr typ = 1 pro získání pouze obrázků galerie (typ=0 je úvodní obrázek)
                pg_cursor.execute(f"""
                    SELECT id_obrazek, soubor, popisek, priorita 
                    FROM {TBL_OBRAZEK} 
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek
                """, (unique_id,))
                gallery_items = pg_cursor.fetchall()
                
                if not gallery_items:
                    logging.info(f"Pro článek ID {article_id} ({article_title}) nebyly nalezeny žádné obrázky v tabulce {TBL_OBRAZEK}.")
                    continue
                
                logging.info(f"Pro článek ID {article_id} ({article_title}) nalezeno {len(gallery_items)} obrázků.")
                
                # 4. Vytvořit WordPress galerii
                gallery_ids = []
                for _, image_file, image_desc, _ in gallery_items:
                    # Pokusit se najít obrázek v mapování
                    if image_file in image_map:
                        attachment_id = image_map[image_file]['wp_id']
                        gallery_ids.append(str(attachment_id))
                    else:
                        # Log warning
                        logging.warning(f"Obrázek '{image_file}' není v mapování.")
                
                if not gallery_ids:
                    logging.warning(f"Pro článek ID {article_id} ({article_title}) nebyly nalezeny žádné migrovatelné obrázky, přeskakuji.")
                    continue
                
                # 5. Vytvořit gallery shortcode
                gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
                
                # 6. Získat stávající obsah článku
                mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
                current_content = mysql_cursor.fetchone()[0]
                
                # 7. Přidat galerii na konec nebo po značce <!-- gallery -->
                if "<!-- gallery -->" in current_content:
                    new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
                else:
                    new_content = current_content + "\n\n" + gallery_shortcode
                
                # 8. Aktualizovat obsah článku
                mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", (new_content, wp_post_id))
                
                # 9. Uložit metadata o galerii
                meta_value = {
                    'original_article_id': article_id,
                    'original_unique_id': unique_id,
                    'article_title': article_title,
                    'image_ids': gallery_ids,
                    'image_count': len(gallery_ids)
                }
                
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
                )
                
                # 10. Aktualizovat mapování
                gallery_map[gallery_key] = {
                    'wp_post_id': wp_post_id,
                    'gallery_shortcode': gallery_shortcode,
                    'image_count': len(gallery_ids)
                }
                
                mysql_conn.commit()
                new_galleries_count += 1
                logging.info(f"Galerie pro článek ID {article_id} ({article_title}) úspěšně vytvořena s {len(gallery_ids)} obrázky")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vytváření galerie pro článek ID {article_id}: {e}")
                failed_galleries_count += 1
        
        # 11. Původní metoda - zpracování galerií z tabulky TBL_GALERIE (ponecháváme pro zpětnou kompatibilitu)
        logging.info("Zkouším najít dodatečné galerie v tabulce TBL_GALERIE...")
        
        # Kontrola, zda tabulka GALERIE obsahuje sloupec id_clanek
        pg_cursor.execute(f"""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = '{TBL_GALERIE.replace('prefix_', '')}'
        """)
        column_names = [row[0] for row in pg_cursor.fetchall()]
        
        if 'id_clanek' in column_names:
            pg_cursor.execute(f"""
                SELECT g.id_galerie, g.id_clanek, g.nazev, g.popis
                FROM {TBL_GALERIE} g
                JOIN {TBL_CLANEK} c ON g.id_clanek = c.id_clanek
                WHERE g.id_clanek IS NOT NULL AND g.active_state = 1
            """)
        else:
            pg_cursor.execute(f"""
                SELECT id_galerie, NULL as id_clanek, nazev, text as popis
                FROM {TBL_GALERIE}
                WHERE active_state = 1
            """)
        galleries = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(galleries)} dodatečných galerií v tabulce {TBL_GALERIE}.")
        
        for gallery_id, article_id, gallery_name, gallery_description in galleries:
            try:
                # Kontrola, zda už byla galerie zpracována
                if str(gallery_id) in gallery_map:
                    logging.info(f"Galerie ID {gallery_id} již byla zpracována, přeskakuji.")
                    continue
                
                # Kontrola, zda existuje odpovídající článek ve WordPress
                if article_id is None:
                    # Galerie není přímo napojena na článek
                    if len(article_map) > 0:
                        demo_article_id = list(article_map.keys())[0]
                        wp_post_id = article_map[demo_article_id]
                        logging.warning(f"Galerie ID {gallery_id} nemá přímé napojení na článek, používám demo článek ID {demo_article_id}.")
                    else:
                        logging.warning(f"Nenalezen žádný článek pro galerii ID {gallery_id}, přeskakuji.")
                        continue
                elif str(article_id) not in article_map:
                    logging.warning(f"Článek ID {article_id} nebyl migrován, nemohu přidat galerii ID {gallery_id}.")
                    continue
                else:
                    wp_post_id = article_map[str(article_id)]
                
                # Získat položky galerie (obrázky)
                pg_cursor.execute(f"""
                    SELECT id_galerie_polozka, soubor, nazev, priorita
                    FROM {TBL_GALERIE_POLOZKA}
                    WHERE galerie_id = %s
                    ORDER BY priorita, id_galerie_polozka
                """, (gallery_id,))
                gallery_items = pg_cursor.fetchall()
                
                if not gallery_items:
                    logging.warning(f"Galerie ID {gallery_id} nemá žádné položky, přeskakuji.")
                    continue
                
                logging.info(f"Galerie ID {gallery_id} obsahuje {len(gallery_items)} položek.")
                
                # Vytvořit WordPress galerii
                gallery_ids = []
                for _, image_file, image_desc, _ in gallery_items:
                    if image_file in image_map:
                        attachment_id = image_map[image_file]['wp_id']
                        gallery_ids.append(str(attachment_id))
                    else:
                        logging.warning(f"Obrázek '{image_file}' není v mapování.")
                
                if not gallery_ids:
                    logging.warning(f"Galerie ID {gallery_id} nemá žádné migrovatelné obrázky, přeskakuji.")
                    continue
                
                # Vytvořit gallery shortcode
                gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
                
                # Získat stávající obsah článku
                mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
                current_content = mysql_cursor.fetchone()[0]
                
                # Přidat galerii na konec nebo po značce <!-- gallery -->
                if "<!-- gallery -->" in current_content:
                    new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
                else:
                    new_content = current_content + "\n\n" + gallery_shortcode
                
                # Aktualizovat obsah článku
                mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", (new_content, wp_post_id))
                
                # Uložit metadata o galerii
                meta_value = {
                    'original_gallery_id': gallery_id,
                    'original_gallery_name': gallery_name,
                    'image_ids': gallery_ids
                }
                
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
                )
                
                # Aktualizovat mapování
                gallery_map[str(gallery_id)] = {
                    'wp_post_id': wp_post_id,
                    'gallery_shortcode': gallery_shortcode,
                    'image_count': len(gallery_ids)
                }
                
                mysql_conn.commit()
                new_galleries_count += 1
                logging.info(f"Galerie ID {gallery_id} úspěšně migrována do článku ID {wp_post_id}")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při migraci galerie ID {gallery_id}: {e}")
                failed_galleries_count += 1
        
        # Uložit mapování
        save_mapping(gallery_map, 'gallery_map.json')
        
    except Exception as e:
        logging.error(f"Obecná chyba v migrate_galleries: {e}")
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace galerií dokončena. Zpracováno celkem {new_galleries_count} nových galerií. Selhalo: {failed_galleries_count}.")

def check_galerie_directories():
    """
    Kontroluje a diagnostikuje adresáře galerie* v OLD_IMAGE_BASE_PATH.
    Pomáhá identifikovat samostatné galerie, které nemusí být přímo spojeny s články.
    
    Returns:
        dict: Mapování adresářů galerie na počet obrázků
    """
    logging.info("Kontroluji adresáře galerie* ve zdrojové cestě...")
    
    galeries_path = os.path.join(OLD_IMAGE_BASE_PATH)
    galerie_dirs = {}
    
    if not os.path.exists(galeries_path):
        logging.error(f"Cesta {galeries_path} neexistuje!")
        return galerie_dirs
    
    try:
        # Získat seznam adresářů začínajících na "galerie"
        for entry in os.listdir(galeries_path):
            if entry.startswith('galerie') and os.path.isdir(os.path.join(galeries_path, entry)):
                dir_path = os.path.join(galeries_path, entry)
                images = [f for f in os.listdir(dir_path) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) 
                          and not any(f.endswith(suffix) for suffix in 
                                     ('_100x67.jpg', '_120x80.jpg', '_68x90.jpg'))]
                
                galerie_id = entry.replace('galerie', '')
                galerie_dirs[galerie_id] = {
                    'path': dir_path,
                    'image_count': len(images),
                    'images': images[:5]  # Jen pár obrázků pro diagnostiku
                }
                
        logging.info(f"Nalezeno {len(galerie_dirs)} potenciálních galerií v adresářích.")
        
        for gal_id, data in list(galerie_dirs.items())[:5]:  # Jen pár pro výpis
            logging.info(f"Galerie ID: {gal_id}, Počet obrázků: {data['image_count']}, "
                         f"Příklady: {', '.join(data['images'][:3])}" +
                         ("..." if len(data['images']) > 3 else ""))
            
        return galerie_dirs
    except Exception as e:
        logging.error(f"Chyba při kontrole adresářů galerie: {e}")
        return {}

def force_migrate_gallery_for_article(article_id, reset=False):
    """
    Vynucená migrace galerie pro konkrétní článek.
    
    Parametry:
    - article_id: ID článku, pro který chceme migrovat galerii
    - reset: Pokud True, bude existující galerie přepsána
    
    Tato funkce je určena pro opravné situace, kdy běžná migrace selhává.
    Obsahuje další diagnostické informace a možnost resetovat existující galerii.
    """
    logging.info(f"Spouštím vynucenou migraci galerie pro článek ID {article_id}...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. Získat informace o článku
        pg_cursor.execute(f"SELECT id_clanek, unikatni_id, nazev FROM {TBL_CLANEK} WHERE id_clanek = %s", (article_id,))
        article = pg_cursor.fetchone()
        
        if not article:
            logging.error(f"Článek s ID {article_id} nebyl nalezen v databázi!")
            return False
        
        article_id, unique_id, article_title = article
        logging.info(f"Nalezen článek: ID={article_id}, Název={article_title}, Unikátní ID={unique_id}")
        
        # 2. Kontrola, zda článek byl migrován do WordPress
        article_map = load_mapping('article_map.json')
        if str(article_id) not in article_map:
            logging.error(f"Článek ID {article_id} nebyl migrován do WordPress!")
            return False
        
        wp_post_id = article_map[str(article_id)]
        logging.info(f"WordPress ID článku: {wp_post_id}")
        
        # 3. Načtení image map
        image_map = load_mapping('image_map.json')
        logging.info(f"Načteno {len(image_map)} mapování obrázků")
        
        # 4. Diagnostika článku a jeho obrázků
        debug_gallery_items(article_id, unique_id, article_title, pg_cursor)
        
        # 5. Získat obrázky typu 1 (galerie)
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, popisek, priorita, typ, link 
            FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s AND active_state = 1 AND typ = 1
            ORDER BY priorita DESC, id_obrazek
        """, (unique_id,))
        gallery_items = pg_cursor.fetchall()
        
        if not gallery_items:
            logging.warning(f"Pro článek nebyly nalezeny žádné obrázky typu 1 (galerie)!")
            
            # Zkusíme najít obrázky bez ohledu na typ
            pg_cursor.execute(f"""
                SELECT id_obrazek, soubor, popisek, priorita, typ, link 
                FROM {TBL_OBRAZEK} 
                WHERE polozka_id = %s AND active_state = 1
                ORDER BY priorita DESC, id_obrazek
            """, (unique_id,))
            all_items = pg_cursor.fetchall()
            
            if all_items:
                logging.info(f"Nalezeno {len(all_items)} aktivních obrázků bez ohledu na typ.")
                gallery_items = all_items
                logging.warning("Použijeme všechny obrázky, včetně typu 0 (hlavní obrázek).")
            else:
                logging.error("Nenalezeny žádné obrázky pro tento článek!")
                return False
        
        logging.info(f"Nalezeno {len(gallery_items)} obrázků pro galerii.")
        
        # 6. Diagnostika souborů obrázků
        missing_files = []
        for _, image_file, _, _, _, _ in gallery_items:
            src_path = os.path.join(OLD_IMAGE_BASE_PATH, image_file)
            if not os.path.exists(src_path):
                missing_files.append(image_file)
                logging.warning(f"Zdrojový soubor '{src_path}' neexistuje!")
        
        if missing_files:
            logging.warning(f"Chybí {len(missing_files)} zdrojových souborů obrázků z celkem {len(gallery_items)}.")
        
        # 7. Kontrola image_map
        not_in_map = []
        for _, image_file, _, _, _, _ in gallery_items:
            if image_file not in image_map:
                not_in_map.append(image_file)
        
        if not_in_map:
            logging.warning(f"{len(not_in_map)} obrázků není v mapování (ze {len(gallery_items)} celkem).")
            logging.warning(f"Chybějící mapování pro: {', '.join(not_in_map[:5])}" + 
                            (f" a dalších {len(not_in_map)-5}" if len(not_in_map) > 5 else ""))
            
        # 8. Zkontrolovat, zda už existuje galerie
        gallery_key = f"article_{article_id}"
        gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
        
        if gallery_key in gallery_map and not reset:
            logging.warning(f"Galerie pro článek ID {article_id} již existuje.")
            logging.warning("Pro přepsání použijte parametr reset=True.")
            return False
        
        # 9. Vytvořit WordPress galerii
        gallery_ids = []
        for _, image_file, _, _, _, link in gallery_items:
            # Pokusit se najít obrázek v mapování
            if image_file in image_map:
                attachment_id = image_map[image_file]['wp_id']
                gallery_ids.append(str(attachment_id))
            elif link:
                # Pokud máme link, zkusíme najít v mapování podle názvu souboru z linku
                # Extrahovat název souboru z linku
                match = re.search(r'/([^/]+)$', link)
                if match:
                    file_name = match.group(1)
                    if file_name in image_map:
                        attachment_id = image_map[file_name]['wp_id']
                        gallery_ids.append(str(attachment_id))
                        logging.info(f"Nalezeno mapování pro obrázek podle názvu souboru z linku: {file_name}")
                    else:
                        # Zkusíme porovnat s názvem bez přípony
                        base_name, ext = os.path.splitext(file_name)
                        for img_key in image_map:
                            if img_key.startswith(base_name):
                                attachment_id = image_map[img_key]['wp_id']
                                gallery_ids.append(str(attachment_id))
                                logging.info(f"Nalezeno mapování pro obrázek pomocí základního názvu: {base_name}")
                                break
        
        if not gallery_ids:
            logging.error("Nebyly nalezeny žádné migrovatelné obrázky pro galerii!")
            return False
        
        logging.info(f"Vytvářím galerii s {len(gallery_ids)} obrázky.")
        
        # 10. Vytvořit gallery shortcode
        gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
        
        # 11. Získat stávající obsah článku
        mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
        current_content = mysql_cursor.fetchone()[0]
        
        # 12. Kontrola, zda článek již obsahuje galerii
        gallery_pattern = r'\[gallery[^\]]*\]'
        has_gallery = re.search(gallery_pattern, current_content)
        
        if has_gallery and not reset:
            logging.warning("Článek již obsahuje galerii! Pro přepsání použijte parametr reset=True.")
            return False
        
        # 13. Přidat/aktualizovat galerii
        if has_gallery and reset:
            # Nahradit existující galerii
            new_content = re.sub(gallery_pattern, gallery_shortcode, current_content)
            logging.info("Nahrazuji existující galerii.")
        elif "<!-- gallery -->" in current_content:
            # Vložit po značce
            new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
            logging.info("Vkládám galerii na místo označené <!-- gallery -->")
        else:
            # Přidat na konec
            new_content = current_content + "\n\n" + gallery_shortcode
            logging.info("Přidávám galerii na konec článku.")
        
        # 14. Aktualizovat obsah článku
        mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", (new_content, wp_post_id))
        
        # 15. Aktualizovat metadata
        if reset:
            # Odstranit existující metadata
            mysql_cursor.execute(f"DELETE FROM {TBL_WP_POSTMETA} WHERE post_id = %s AND meta_key = 'sabre_gallery'", (wp_post_id,))
            logging.info("Odstraněna existující metadata galerie.")
        
        # 16. Uložit nová metadata
        meta_value = {
            'original_article_id': article_id,
            'original_unique_id': unique_id,
            'article_title': article_title,
            'image_ids': gallery_ids,
            'image_count': len(gallery_ids),
            'forced_migration': True,
            'migration_timestamp': datetime.now().isoformat()
        }
        
        mysql_cursor.execute(
            f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
            (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
        )
        
        # 17. Aktualizovat mapování
        gallery_map[gallery_key] = {
            'wp_post_id': wp_post_id,
            'gallery_shortcode': gallery_shortcode,
            'image_count': len(gallery_ids),
            'forced': True
        }
        
        save_mapping(gallery_map, 'gallery_map.json')
        
        mysql_conn.commit()
        logging.info(f"Galerie úspěšně {'aktualizována' if reset else 'vytvořena'} s {len(gallery_ids)} obrázky.")
        return True
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při vynucené migraci galerie: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return False
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

def migrate_standalone_galleries(force=False, reset=False):
    """
    Migruje samostatné galerie, které jsou uloženy v adresářích galerie*.
    Tyto galerie mohou být nezávislé na článcích nebo propojené přes tabulku prefix_galerie.
    
    Args:
        force (bool): Pokud True, provede vynucenou migraci i při chybějících odkazech
        reset (bool): Pokud True, přepíše existující galerie
    
    Returns:
        int: Počet úspěšně migrovaných galerií
    """
    logging.info("Spouštím migraci samostatných galerií...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    # Načíst mapování
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')
    gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
    
    # Najít adresáře galerií
    galerie_dirs = check_galerie_directories()
    
    # Počítadla
    success_count = 0
    failed_count = 0
    
    try:
        # Získat galerie z databáze
        pg_cursor.execute(f"""
            SELECT id_galerie, nazev, id_clanek, active_state
            FROM {TBL_GALERIE}
            WHERE active_state = 1
        """)
        db_galleries = {str(row[0]): {'name': row[1], 'article_id': row[2]} for row in pg_cursor.fetchall()}
        logging.info(f"Nalezeno {len(db_galleries)} galerií v databázi.")
        
        # Pro každý adresář galerie
        for gal_id, gal_data in galerie_dirs.items():
            try:
                # Přeskočit, pokud galerie už byla zpracována
                if gal_id in gallery_map and not reset:
                    logging.info(f"Galerie ID {gal_id} již byla zpracována, přeskakuji.")
                    continue
                
                gallery_name = db_galleries.get(gal_id, {}).get('name', f"Galerie {gal_id}")
                article_id = db_galleries.get(gal_id, {}).get('article_id')
                
                logging.info(f"Zpracovávám galerii ID {gal_id}, Název: {gallery_name}")
                
                # Najít WordPress článek pro galerii
                wp_post_id = None
                if article_id is not None and str(article_id) in article_map:
                    wp_post_id = article_map[str(article_id)]
                    logging.info(f"Galerie je přiřazena k článku ID {article_id} (WP ID: {wp_post_id}).")
                else:
                    # Pokud nemáme přiřazený článek, použijeme první článek v mapování
                    if len(article_map) > 0:
                        first_article_id = list(article_map.keys())[0]
                        wp_post_id = article_map[first_article_id]
                        logging.warning(f"Galerie nemá přiřazený článek, používám ukázkový WP ID: {wp_post_id}")
                    else:
                        logging.error("Nenalezen žádný cílový článek pro galerii, přeskakuji.")
                        failed_count += 1
                        continue
                
                # Získat obrázky z adresáře
                gallery_dir = gal_data['path']
                image_files = [f for f in os.listdir(gallery_dir) 
                              if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) 
                              and not any(f.endswith(suffix) for suffix in 
                                         ('_100x67.jpg', '_120x80.jpg', '_68x90.jpg'))]
                
                if not image_files:
                    logging.warning(f"Adresář galerie {gal_id} neobsahuje žádné použitelné obrázky.")
                    failed_count += 1
                    continue
                
                logging.info(f"Nalezeno {len(image_files)} obrázků v adresáři galerie.")
                
                # Vytvořit WordPress galerii
                gallery_ids = []
                missing_files = []
                
                for img_file in image_files:
                    # Zkusit najít obrázek v mapování
                    full_path_in_src = os.path.join("galerie" + gal_id, img_file)
                    
                    if full_path_in_src in image_map:
                        attachment_id = image_map[full_path_in_src]['wp_id']
                        gallery_ids.append(str(attachment_id))
                    elif img_file in image_map:
                        attachment_id = image_map[img_file]['wp_id']
                        gallery_ids.append(str(attachment_id))
                    else:
                        # Zkusíme porovnat s názvem bez přípony
                        base_name, ext = os.path.splitext(img_file)
                        found = False
                        for img_key in image_map:
                            if os.path.basename(img_key).startswith(base_name):
                                attachment_id = image_map[img_key]['wp_id']
                                gallery_ids.append(str(attachment_id))
                                logging.info(f"Nalezeno mapování pro {img_file} pomocí základního názvu: {base_name}")
                                found = True
                                break
                        
                        if not found:
                            missing_files.append(img_file)
                
                if missing_files:
                    logging.warning(f"Nenalezeno mapování pro {len(missing_files)} obrázků: {', '.join(missing_files[:3])}"
                                   + ("..." if len(missing_files) > 3 else ""))
                
                if not gallery_ids:
                    logging.error(f"Nenalezeny žádné migrovatelné obrázky pro galerii {gal_id}.")
                    failed_count += 1
                    continue
                
                # Vytvořit gallery shortcode
                gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
                
                # Získat stávající obsah článku
                mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
                current_content = mysql_cursor.fetchone()[0]
                
                # Zkontrolovat existující galerii
                gallery_pattern = r'\[gallery[^\]]*\]'
                has_gallery = re.search(gallery_pattern, current_content)
                
                if has_gallery and not reset:
                    logging.warning(f"Článek již obsahuje galerii. Pro přepsání použijte reset=True.")
                    continue
                
                # Aktualizovat obsah článku
                if has_gallery and reset:
                    new_content = re.sub(gallery_pattern, gallery_shortcode, current_content)
                    logging.info("Nahrazuji existující galerii.")
                elif "<!-- gallery -->" in current_content:
                    new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
                    logging.info("Vkládám galerii na místo označené <!-- gallery -->")
                else:
                    new_content = current_content + "\n\n" + gallery_shortcode
                    logging.info("Přidávám galerii na konec článku.")
                
                mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", 
                                    (new_content, wp_post_id))
                
                # Aktualizovat metadata
                if reset:
                    mysql_cursor.execute(f"DELETE FROM {TBL_WP_POSTMETA} WHERE post_id = %s AND meta_key = 'sabre_gallery'", 
                                       (wp_post_id,))
                
                meta_value = {
                    'original_gallery_id': gal_id,
                    'gallery_name': gallery_name,
                    'original_article_id': article_id,
                    'image_count': len(gallery_ids),
                    'migration_timestamp': datetime.now().isoformat()
                }
                
                mysql_cursor.execute(
                    f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
                )
                
                # Aktualizovat mapování
                gallery_map[gal_id] = {
                    'wp_post_id': wp_post_id,
                    'gallery_shortcode': gallery_shortcode,
                    'image_count': len(gallery_ids),
                    'migration_type': 'standalone'
                }
                
                mysql_conn.commit()
                success_count += 1
                logging.info(f"Galerie ID {gal_id} úspěšně migrována s {len(gallery_ids)} obrázky.")
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při migraci galerie ID {gal_id}: {e}")
                import traceback
                logging.error(traceback.format_exc())
                failed_count += 1
        
        # Uložit aktualizované mapování
        save_mapping(gallery_map, 'gallery_map.json')
        logging.info(f"Migrace samostatných galerií dokončena. Úspěšně: {success_count}, Selhalo: {failed_count}")
        return success_count
    
    except Exception as e:
        logging.error(f"Obecná chyba při migraci samostatných galerií: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return 0
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

if __name__ == "__main__":
    # Kontrola argumentů pro cílení na konkrétní článek
    target_article_id = None
    force_mode = False
    reset_gallery = False
    
    # Zpracování argumentů příkazové řádky
    for i, arg in enumerate(sys.argv[1:], 1):
        if arg == '--force' or arg == '-f':
            force_mode = True
            continue
        if arg == '--reset' or arg == '-r':
            reset_gallery = True
            continue
        try:
            target_article_id = int(arg)
            print(f"Cílový článek ID: {target_article_id}")
        except ValueError:
            if '=' in arg:  # Formát klíč=hodnota
                key, value = arg.split('=', 1)
                if key == 'id':
                    try:
                        target_article_id = int(value)
                        print(f"Cílový článek ID: {target_article_id}")
                    except ValueError:
                        print(f"Neplatné ID článku: {value}")
            else:
                print(f"Neplatný argument: {arg}")
    
    # Alternativně lze ID článku zadat pomocí proměnné prostředí
    if not target_article_id and os.environ.get('TARGET_ARTICLE_ID'):
        try:
            target_article_id = int(os.environ.get('TARGET_ARTICLE_ID'))
            print(f"Spouštím migraci pro konkrétní článek ID z proměnné prostředí: {target_article_id}")
        except ValueError:
            print(f"Neplatné ID článku v proměnné prostředí: {os.environ.get('TARGET_ARTICLE_ID')}")
            sys.exit(1)
    
    # Kontrola argumentů pro pokročilé možnosti migrace
    migrate_standalone = False
    list_galleries = False
    
    # Analýza argumentů
    for i, arg in enumerate(sys.argv[1:], 1):
        if arg == '--standalone' or arg == '-s':
            migrate_standalone = True
            continue
        if arg == '--list-galleries' or arg == '-l':
            list_galleries = True
            continue
    
    # Výpis dostupných galerií
    if list_galleries:
        print("=== Seznam dostupných galerií ===")
        galerie_dirs = check_galerie_directories()
        print(f"Nalezeno {len(galerie_dirs)} potenciálních samostatných galerií v adresářích.")
        sys.exit(0)
        
    # Speciální případ pro článek z příkladu
    if target_article_id == 28931 or os.environ.get('EXAMPLE_ARTICLE'):
        print("Vynucená migrace galerie pro ukázkový článek ID 28931...")
        if force_migrate_gallery_for_article(28931, reset=reset_gallery):
            print("Vynucená migrace galerie úspěšně dokončena.")
            if not migrate_standalone:
                sys.exit(0)
        else:
            print("Vynucená migrace galerie selhala. Zkontrolujte logy.")
            if not migrate_standalone:
                sys.exit(1)
    
    # Migrace samostatných galerií
    if migrate_standalone:
        print("Spouštím migraci samostatných galerií...")
        count = migrate_standalone_galleries(force=force_mode, reset=reset_gallery)
        print(f"Migrace samostatných galerií dokončena. Úspěšně zpracováno: {count}")
        sys.exit(0)
        
    # Standardní migrace článkových galerií
    if force_mode and target_article_id:
        # Vynucený režim pro konkrétní článek
        if force_migrate_gallery_for_article(target_article_id, reset=reset_gallery):
            print(f"Vynucená migrace galerie pro článek ID {target_article_id} úspěšně dokončena.")
        else:
            print(f"Vynucená migrace galerie pro článek ID {target_article_id} selhala. Zkontrolujte logy.")
    else:
        # Běžná migrace
        migrate_galleries(target_article_id)
