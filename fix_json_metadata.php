<?php
/**
 * Fix corrupted JSON metadata in WordPress database
 * This script repairs corrupted sabre_gallery meta entries
 * 
 * Usage: php fix_json_metadata.php
 */

// Load WordPress core
require_once(dirname(__DIR__) . '/../../../wp-load.php');

// Set up logging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$log_file = __DIR__ . '/logs/json_metadata_fix_' . date('Ymd_His') . '.log';
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

/**
 * Write message to log file
 */
function log_message($message, $type = 'INFO') {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] [$type] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND);
    echo $log_entry;
}

log_message("Starting JSON metadata fix script");

// Get all sabre_gallery metadata entries
global $wpdb;
$meta_entries = $wpdb->get_results(
    "SELECT meta_id, post_id, meta_value FROM {$wpdb->postmeta} WHERE meta_key = 'sabre_gallery'"
);

log_message("Found " . count($meta_entries) . " sabre_gallery metadata entries");

$fixed_count = 0;
$already_valid_count = 0;
$failed_count = 0;

foreach ($meta_entries as $entry) {
    $meta_id = $entry->meta_id;
    $post_id = $entry->post_id;
    $meta_value = $entry->meta_value;
    
    // Test if JSON is valid
    $decoded = json_decode($meta_value, true);
    $is_valid = (json_last_error() === JSON_ERROR_NONE);
    
    if ($is_valid) {
        // JSON is already valid
        log_message("Meta ID $meta_id for Post ID $post_id has valid JSON", 'OK');
        $already_valid_count++;
        continue;
    }
    
    // Let's try to fix the JSON
    log_message("Meta ID $meta_id for Post ID $post_id has invalid JSON: " . json_last_error_msg(), 'ERROR');
    
    // Find gallery shortcode in post content to extract image IDs
    $post_content = $wpdb->get_var($wpdb->prepare(
        "SELECT post_content FROM {$wpdb->posts} WHERE ID = %d",
        $post_id
    ));
    
    $image_ids = [];
    if ($post_content && preg_match('/\[gallery.*?ids="([^"]+)".*?\]/i', $post_content, $matches)) {
        $image_ids = explode(',', $matches[1]);
        $image_ids = array_map('trim', $image_ids);
        log_message("Found " . count($image_ids) . " image IDs in gallery shortcode for Post ID $post_id", 'INFO');
    } else {
        // Try to extract image IDs from corrupted JSON
        if (preg_match('/"image_ids":\s*\[(.*?)\]/s', $meta_value, $matches)) {
            $ids_string = $matches[1];
            // Remove all non-numeric characters, commas, and quotes
            $ids_string = preg_replace('/[^0-9,"]/', '', $ids_string);
            $ids_string = str_replace('"', '', $ids_string);
            $image_ids = explode(',', $ids_string);
            $image_ids = array_filter($image_ids); // Remove empty entries
            log_message("Extracted " . count($image_ids) . " image IDs from corrupted JSON for Post ID $post_id", 'INFO');
        }
    }
    
    // Extract other fields if possible
    $original_article_id = null;
    if (preg_match('/"original_article_id":\s*(\d+)/', $meta_value, $matches)) {
        $original_article_id = (int)$matches[1];
    }
    
    $original_unique_id = '';
    if (preg_match('/"original_unique_id":\s*"([^"]+)"/', $meta_value, $matches)) {
        $original_unique_id = $matches[1];
    }
    
    $article_title = '';
    if (preg_match('/"article_title":\s*"([^"]+)"/', $meta_value, $matches)) {
        $article_title = $matches[1];
    } else {
        // Get post title if available
        $post_title = $wpdb->get_var($wpdb->prepare(
            "SELECT post_title FROM {$wpdb->posts} WHERE ID = %d",
            $post_id
        ));
        if ($post_title) {
            $article_title = $post_title;
        }
    }
    
    // Create new valid JSON
    $new_meta = [
        'original_article_id' => $original_article_id,
        'original_unique_id' => $original_unique_id,
        'article_title' => $article_title,
        'image_ids' => $image_ids,
        'image_count' => count($image_ids),
        'fixed_metadata' => true,
        'fixed_timestamp' => date('c')
    ];
    
    $new_json = json_encode($new_meta, JSON_UNESCAPED_UNICODE);
    
    // Check if the new JSON is valid
    $test_decode = json_decode($new_json, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        log_message("Failed to create valid JSON for Meta ID $meta_id: " . json_last_error_msg(), 'ERROR');
        $failed_count++;
        continue;
    }
    
    // Update the metadata
    $result = $wpdb->update(
        $wpdb->postmeta,
        ['meta_value' => $new_json],
        ['meta_id' => $meta_id]
    );
    
    if ($result !== false) {
        log_message("Fixed Meta ID $meta_id for Post ID $post_id with " . count($image_ids) . " image IDs", 'SUCCESS');
        $fixed_count++;
    } else {
        log_message("Failed to update Meta ID $meta_id in database", 'ERROR');
        $failed_count++;
    }
}

log_message("=== Summary ===");
log_message("Total entries: " . count($meta_entries));
log_message("Already valid: $already_valid_count");
log_message("Fixed: $fixed_count");
log_message("Failed: $failed_count");
log_message("Log file: $log_file");
