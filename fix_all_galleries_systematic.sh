#!/bin/bash

# Skript pro systematickou opravu všech galerií

# Nastavení cesty k logovacímu adresáři
LOG_DIR="logs"
mkdir -p $LOG_DIR

# Datum a čas pro název logu
DATE=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/fix_all_galleries_systematic_$DATE.log"

echo "Spouštím systémovou opravu všech galerií..."
echo "Výsledky budou uloženy do $LOG_FILE"

# Nejprve provedeme test s dry-run a limitem
echo "Provádím test s dry-run a limitem 3 článků..."
python3 fix_all_galleries_systematic.py --dry-run --limit 3 --verbose | tee "$LOG_FILE.test"

# Zeptáme se, zda má pokračovat s opravou všech článků
echo
echo "Test byl dokončen. Chcete pokračovat s opravou všech galerií? (y/n)"
read -p "> " choice
if [[ "$choice" == "y" || "$choice" == "Y" ]]; then
    echo "Spouštím opravu všech galerií..."
    python3 fix_all_galleries_systematic.py --verbose | tee $LOG_FILE
    echo
    echo "Oprava dokončena. Log je uložen v: $LOG_FILE"
else
    echo "Oprava byla zrušena."
    echo "Pro provedení opravy spusťte:"
    echo "  python3 fix_all_galleries_systematic.py"
    echo "nebo pro opravu jednoho článku:"
    echo "  python3 fix_gallery_mapping_add_images.py --wp-id ID_CLANKU_WP --pg-id ID_CLANKU_PG"
fi
