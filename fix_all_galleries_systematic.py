#!/usr/bin/env python3
import os
import json
import logging
import argparse
from datetime import datetime
from fix_gallery_mapping_add_images import fix_gallery_for_article, get_pg_connection, get_mysql_connection, get_table_prefixes, load_mapping

# Nastavení logování
LOG_FILE = os.path.join('logs', f'fix_all_galleries_systematic_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def fix_all_galleries_systematic(dry_run=False, limit=None, start_from=0, verbose=False):
    """
    Systematicky opraví všechny neúplné galerie článků.
    
    1. Najít články s více obrázky v PostgreSQL
    2. Pro každý takový článek najít odpovídající WordPress článek pomocí metadat
    3. Porovnat počty obrázků a opravit články s neúplnými galeriemi
    
    Args:
        dry_run: Pokud True, neprovede skutečné změny
        limit: Maximální počet článků k opravě (None = všechny)
        start_from: Od jakého indexu začít (pro možnost opakování po přerušení)
        verbose: Pokud True, vypisuje podrobnější informace
    """
    start_time = datetime.now()
    logging.info(f"Spouštím systematickou opravu všech galerií (dry_run={dry_run}, limit={limit}, start_from={start_from})...")
    
    # Připojení k databázím
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor(dictionary=True)
    
    # Získání prefixů tabulek
    wp_prefix, sabre_prefix = get_table_prefixes()
    
    try:
        # 1. Najít články s více obrázky v PostgreSQL
        pg_cursor.execute(f"""
            SELECT c.id_clanek, c.unikatni_id, c.nazev, COUNT(o.id_obrazek) as obrazku
            FROM {sabre_prefix}clanek c
            JOIN {sabre_prefix}obrazek o ON o.polozka_id = c.unikatni_id
            WHERE o.active_state = 1 AND o.typ = 1
            GROUP BY c.id_clanek, c.unikatni_id, c.nazev
            HAVING COUNT(o.id_obrazek) > 1
            ORDER BY COUNT(o.id_obrazek) DESC
        """)
        
        pg_articles = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(pg_articles)} článků s více obrázky v PostgreSQL.")
        
        # Statistiky
        total_fixed = 0
        total_skipped = 0
        total_failed = 0
        
        # 2. Pro každý PostgreSQL článek najít odpovídající WordPress článek
        count = 0
        for i, pg_article in enumerate(pg_articles[start_from:], start=start_from):
            pg_id, unique_id, title, img_count = pg_article
            
            # Zkontrolovat limit
            if limit and count >= limit:
                logging.info(f"Dosažen limit {limit} článků, končím.")
                break
                
            # Načíst mapování článků
            article_map = load_mapping('article_map.json')
            if not article_map:
                logging.error("Chybí mapování článků, nebudu moci najít WordPress ekvivalenty.")
                return
            
            # 1. Zkusit najít v mapování článků
            wp_id = None
            if str(pg_id) in article_map:
                wp_id = article_map[str(pg_id)]
                
                # Ověřit existenci článku ve WordPress
                mysql_cursor.execute(f"""
                    SELECT ID FROM {wp_prefix}posts 
                    WHERE ID = %s AND post_type = 'post'
                """, (wp_id,))
                
                if mysql_cursor.fetchone():
                    logging.info(f"Nalezen WordPress článek z mapování: PG ID {pg_id} -> WP ID {wp_id}")
                    
                    # Zjistit, zda má článek sabre_gallery metadata
                    mysql_cursor.execute(f"""
                        SELECT meta_id, meta_value 
                        FROM {wp_prefix}postmeta 
                        WHERE post_id = %s AND meta_key = 'sabre_gallery'
                    """, (wp_id,))
                    
                    meta_data = mysql_cursor.fetchone()
                    
                    if not meta_data:
                        # Článek nemá metadata galerie, vytvoříme nová
                        if verbose:
                            logging.info(f"Článek WP ID {wp_id} nemá metadata sabre_gallery, vytvořím nová.")
                            
                        # Připravit nová metadata
                        gallery_data = {
                            'original_article_id': pg_id,
                            'original_unique_id': unique_id,
                            'article_title': title,
                            'image_ids': [],
                            'image_count': 0
                        }
                        
                        # Vložit metadata
                        if not dry_run:
                            mysql_cursor.execute(f"""
                                INSERT INTO {wp_prefix}postmeta (post_id, meta_key, meta_value)
                                VALUES (%s, %s, %s)
                            """, (wp_id, 'sabre_gallery', json.dumps(gallery_data)))
                            mysql_conn.commit()
                        
                        # Nastavit wp_article s post_id a meta_value
                        wp_article = {'post_id': wp_id, 'meta_value': json.dumps(gallery_data)}
                    else:
                        # Použít existující metadata
                        wp_article = {'post_id': wp_id, 'meta_value': meta_data['meta_value']}
                else:
                    wp_id = None
            
            # 2. Pokud jsme nenašli v mapování, zkusíme najít podle metadat
            if not wp_id:
                # Hledat WordPress článek podle metadat
                mysql_cursor.execute(f"""
                    SELECT post_id, meta_value 
                    FROM {wp_prefix}postmeta 
                    WHERE meta_key = 'sabre_gallery' 
                    AND meta_value LIKE %s
                """, (f'%"original_unique_id":"{unique_id}"%',))
                
                wp_article = mysql_cursor.fetchone()
                
                if not wp_article:
                    # Zkusíme hledat podle original_article_id
                    mysql_cursor.execute(f"""
                        SELECT post_id, meta_value 
                        FROM {wp_prefix}postmeta 
                        WHERE meta_key = 'sabre_gallery' 
                        AND meta_value LIKE %s
                    """, (f'%"original_article_id":{pg_id}%',))
                    
                    wp_article = mysql_cursor.fetchone()
            
            if not wp_article:
                logging.warning(f"Pro PostgreSQL článek ID {pg_id} ({title}) nebyl nalezen WordPress ekvivalent.")
                total_skipped += 1
                continue
                
            wp_id = wp_article['post_id']
            
            # Zjistit počet obrázků v WordPress
            try:
                gallery_data = json.loads(wp_article['meta_value'])
                wp_img_count = len(gallery_data.get('image_ids', []))
                
                # Pokud má WordPress stejně nebo více obrázků, přeskočit
                if wp_img_count >= img_count:
                    if verbose:
                        logging.info(f"Článek PG ID {pg_id}, WP ID {wp_id} ({title}): má {wp_img_count} obrázků ve WP, {img_count} v PG - přeskakuji.")
                    total_skipped += 1
                    continue
                
                # Opravit galerii
                logging.info(f"Opravuji galerii pro článek PG ID {pg_id}, WP ID {wp_id} ({title}): má {wp_img_count} obrázků ve WP, {img_count} v PG.")
                
                try:
                    fix_gallery_for_article(wp_id, pg_id, dry_run)
                    total_fixed += 1
                    count += 1
                    logging.info(f"Článek PG ID {pg_id}, WP ID {wp_id} ({title}) úspěšně opraven.")
                except Exception as e:
                    logging.error(f"Chyba při opravě galerie pro článek PG ID {pg_id}, WP ID {wp_id} ({title}): {e}")
                    import traceback
                    logging.error(traceback.format_exc())
                    total_failed += 1
                    
            except json.JSONDecodeError:
                logging.error(f"Neplatný JSON v metadatech pro WP ID {wp_id}")
                total_failed += 1
                continue
            except Exception as e:
                logging.error(f"Chyba při zpracování dat galerie pro článek PG ID {pg_id}, WP ID {wp_id}: {e}")
                import traceback
                logging.error(traceback.format_exc())
                total_failed += 1
                
        # Výsledné statistiky
        logging.info("-" * 50)
        logging.info("SOUHRN OPRAVY GALERIÍ:")
        logging.info(f"Celkem opraveno: {total_fixed}")
        logging.info(f"Celkem přeskočeno: {total_skipped}")
        logging.info(f"Celkem selhalo: {total_failed}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        logging.info(f"Oprava dokončena za {duration.total_seconds():.2f} sekund.")
        
        return total_fixed
            
    except Exception as e:
        logging.error(f"Obecná chyba: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return 0
    finally:
        # Zavřít spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Systematicky opraví všechny galerie článků')
    parser.add_argument('--dry-run', action='store_true', help='Pouze simulovat opravy bez ukládání')
    parser.add_argument('--limit', type=int, help='Maximální počet článků k opravě')
    parser.add_argument('--start-from', type=int, default=0, help='Od jakého indexu začít')
    parser.add_argument('--verbose', action='store_true', help='Výpis podrobnějších informací')
    args = parser.parse_args()
    
    fix_all_galleries_systematic(args.dry_run, args.limit, args.start_from, args.verbose)
