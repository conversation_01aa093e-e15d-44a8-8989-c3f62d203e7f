#!/bin/bash

# Script pro opravu cest k obrázkům v obsahu článků
# Automatizovaný wrapper pro fix_content_image_paths.py

# Barvy pro výpis
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Vytvořit složku pro logy, pokud neexistuje
mkdir -p logs

# Název log souboru
LOG_FILE="logs/fix_content_image_paths_$(date +%Y%m%d_%H%M%S).log"

echo -e "${GREEN}===== OPRAVA CEST K OBRÁZKŮM V OBSAHU ČLÁNKŮ =====${NC}"
echo "Výstup bude zaznamenán do: $LOG_FILE"

# Funkce pro zobrazení nápovědy
show_help() {
    echo "Použití: $0 [MOŽNOSTI]"
    echo ""
    echo "Možnosti:"
    echo "  -h, --help          <PERSON>obraz<PERSON> tuto nápovědu"
    echo "  -a, --all           Opraví všechny články"
    echo "  -i, --id ID         Opraví pouze článek s daným WordPress ID"
    echo "  -l, --limit POČET   Maximální počet článků ke zpracování"
    echo "  -s, --start POZICE  Index, od kterého začít zpracování"
    echo "  -d, --dry-run       Pouze simulace bez skutečných změn"
    echo "  -v, --verbose       Podrobnější výpis průběhu"
    echo ""
    echo "Příklad:"
    echo "  $0 --all --limit 100       # Opraví prvních 100 článků"
    echo "  $0 --id 1234               # Opraví článek s ID 1234"
    echo "  $0 --all --dry-run         # Simulace opravy všech článků"
    echo ""
}

# Výchozí hodnoty parametrů
ALL=false
DRY_RUN=false
VERBOSE=false
WP_ID=""
LIMIT=""
START_FROM=0

# Zpracování parametrů
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -a|--all)
            ALL=true
            shift
            ;;
        -i|--id)
            WP_ID="$2"
            shift 2
            ;;
        -l|--limit)
            LIMIT="$2"
            shift 2
            ;;
        -s|--start)
            START_FROM="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo -e "${RED}Neznámý parametr: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Kontrola konfliktu parametrů
if [[ "$ALL" == true && -n "$WP_ID" ]]; then
    echo -e "${RED}Chyba: Nelze kombinovat parametry --all a --id.${NC}"
    show_help
    exit 1
fi

# Sestavení příkazu
CMD="python3 fix_content_image_paths.py"

if [[ "$ALL" == true ]]; then
    CMD="$CMD --all"
    echo -e "${GREEN}Režim:${NC} Oprava všech článků"
elif [[ -n "$WP_ID" ]]; then
    CMD="$CMD --wp-id $WP_ID"
    echo -e "${GREEN}Režim:${NC} Oprava článku s ID $WP_ID"
else
    echo -e "${RED}Chyba: Musíte zadat buď --all nebo --id.${NC}"
    show_help
    exit 1
fi

if [[ -n "$LIMIT" ]]; then
    CMD="$CMD --limit $LIMIT"
    echo -e "${GREEN}Limit:${NC} $LIMIT článků"
fi

if [[ "$START_FROM" -gt 0 ]]; then
    CMD="$CMD --start-from $START_FROM"
    echo -e "${GREEN}Start od:${NC} pozice $START_FROM"
fi

if [[ "$DRY_RUN" == true ]]; then
    CMD="$CMD --dry-run"
    echo -e "${YELLOW}Režim:${NC} Pouze simulace (dry-run)"
fi

if [[ "$VERBOSE" == true ]]; then
    CMD="$CMD --verbose"
    echo -e "${GREEN}Výpis:${NC} Podrobný"
fi

# Spuštění příkazu
echo -e "${GREEN}Spouštím:${NC} $CMD"
echo "----------------------------------------"

# Zaznamenat příkaz do logu
echo "Příkaz: $CMD" > "$LOG_FILE"
echo "Čas spuštění: $(date)" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# Spustit příkaz a přesměrovat výstup do logu i na obrazovku
eval "$CMD" 2>&1 | tee -a "$LOG_FILE"

# Kontrola návratového kódu
EXIT_CODE=${PIPESTATUS[0]}

echo "----------------------------------------"
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}Oprava cest k obrázkům byla dokončena.${NC}"
else
    echo -e "${RED}Oprava cest k obrázkům selhala s kódem $EXIT_CODE${NC}"
fi

echo -e "Podrobný log: ${YELLOW}$LOG_FILE${NC}"
