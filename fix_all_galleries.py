#!/usr/bin/env python3
import logging
import os
import json
import sys
import re
import mysql.connector
import psycopg2
from datetime import datetime
import argparse

# Nastavení logování
LOG_FILE = os.path.join('logs', f'fix_all_galleries_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_pg_connection():
    """
    Vytvoří a vrátí spojení s PostgreSQL databází pro SABRE
    na základě konfigurace v .env souboru
    """
    # Načtení konfigurace z .env souboru
    pg_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('PG_'):
                    pg_config[key[3:].lower()] = value

    return psycopg2.connect(
        host=pg_config.get('host', 'localhost'),
        user=pg_config.get('user', ''),
        password=pg_config.get('password', ''),
        database=pg_config.get('dbname', ''),
        port=int(pg_config.get('port', 5432))
    )

def get_mysql_connection():
    """
    Vytvoří a vrátí spojení s MySQL databází pro WordPress 
    na základě konfigurace v .env souboru
    """
    # Načtení konfigurace z .env souboru
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def save_mapping(data, filename):
    """Uloží mapování do souboru."""
    try:
        os.makedirs('mappings', exist_ok=True)
        filepath = os.path.join('mappings', filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logging.error(f"Chyba při ukládání mapování {filename}: {e}")
        return False

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    sabre_prefix = 'prefix_'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
                elif line.startswith('SABRE_TABLE_PREFIX='):
                    sabre_prefix = line.split('=', 1)[1]
    
    return wp_prefix, sabre_prefix

def get_image_base_path():
    """Získá základní cestu k obrázkům."""
    path = '/home/<USER>/sabre/public_html_backup/public_html/obrazek'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('OLD_IMAGE_BASE_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def find_wp_image_ids(unique_id, obrazky, image_map, mysql_conn, wp_prefix):
    """
    Funkce pro nalezení WP ID obrázků pomocí různých strategií mapování.
    Implementuje několik strategií pro nalezení WP ID obrázků.
    """
    wp_image_ids = []
    mysql_cursor = mysql_conn.cursor(dictionary=True)
    
    # Funkce pro přidání unikátních ID obrázků
    def add_unique_image_id(image_id):
        if image_id and str(image_id) not in wp_image_ids:
            wp_image_ids.append(str(image_id))
            return True
        return False
    
    # 1. Mapování přímé - zkusit najít přímo v image_map
    for _, img_file, _, _ in obrazky:
        if img_file in image_map:
            wp_id = image_map[img_file].get('wp_id')
            add_unique_image_id(wp_id)
        
        # Zkusit s cestou
        path_with_unique_id = os.path.join(unique_id, img_file)
        if path_with_unique_id in image_map:
            wp_id = image_map[path_with_unique_id].get('wp_id')
            add_unique_image_id(wp_id)
    
    # 2. Mapování podle základního názvu souboru
    for _, img_file, _, _ in obrazky:
        base_name = os.path.splitext(os.path.basename(img_file))[0]
        
        for map_key, map_data in image_map.items():
            map_basename = os.path.splitext(os.path.basename(map_key))[0]
            
            # Přesná shoda základního názvu
            if map_basename == base_name:
                wp_id = map_data.get('wp_id')
                add_unique_image_id(wp_id)
            
            # Částečná shoda - jeden je substring druhého
            elif map_basename.startswith(base_name) or base_name.startswith(map_basename):
                wp_id = map_data.get('wp_id')
                add_unique_image_id(wp_id)
    
    # 3. Mapování podle numerické části názvu
    for _, img_file, _, _ in obrazky:
        # Extrahovat numerickou část pomocí různých regex vzorů
        num_patterns = [
            r'-(\d+)-',  # např. kuzmice-0044-654fe656ebdfc.jpg
            r'-(\d+)\.',  # např. kuzmice-0044.jpg
            r'(\d+)',     # jakékoli číslo v názvu
        ]
        
        for pattern in num_patterns:
            match = re.search(pattern, img_file)
            if match:
                num_part = match.group(1)
                for map_key, map_data in image_map.items():
                    if num_part in map_key:
                        wp_id = map_data.get('wp_id')
                        if add_unique_image_id(wp_id):
                            break
    
    # 4. Hledání v adresářové struktuře, pokud známe unique_id
    image_base_path = get_image_base_path()
    article_dir = os.path.join(image_base_path, unique_id)
    
    if os.path.exists(article_dir) and os.path.isdir(article_dir):
        files = os.listdir(article_dir)
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
        
        # Sestavit mapování vzorů názvů souborů
        img_patterns = {}
        for _, image_file, _, _ in obrazky:
            base_name = os.path.splitext(os.path.basename(image_file))[0]
            img_patterns[base_name] = image_file
        
        # Procházet soubory v adresáři a mapovat
        for file_name in image_files:
            if file_name.endswith(('_100x67.jpg', '_120x80.jpg', '_68x90.jpg')):
                continue  # Přeskočit náhledy
            
            # Hledat podle přesného názvu
            full_path = os.path.join(unique_id, file_name)
            if full_path in image_map:
                add_unique_image_id(image_map[full_path]['wp_id'])
                continue
            
            # Pokud nenalezeno přímo, hledat podle vzoru názvu
            for pattern in img_patterns:
                if file_name.startswith(pattern):
                    # Zkusit najít soubor v image_map
                    for img_key, img_data in image_map.items():
                        if os.path.basename(img_key).startswith(pattern):
                            add_unique_image_id(img_data.get('wp_id'))
                            break
    
    # 5. Pokud stále nemáme dostatek obrázků, zkusíme hledat podle ID v WordPress
    if len(wp_image_ids) < len(obrazky):
        for img_id, _, _, _ in obrazky:
            # Hledat v MySQL WordPress podle meta hodnoty 'sabre_image_id'
            mysql_cursor.execute(f"""
                SELECT post_id FROM {wp_prefix}postmeta 
                WHERE meta_key = 'sabre_image_id' AND meta_value = %s
            """, (img_id,))
            result = mysql_cursor.fetchone()
            if result:
                add_unique_image_id(result['post_id'])
    
    mysql_cursor.close()
    return wp_image_ids

def fix_all_galleries(dry_run=False, verbose=False):
    """
    Univerzální oprava galerií pro všechny články v databázi.
    Kombinuje různé přístupy pro maximální účinnost.
    
    Args:
        dry_run (bool): Pokud True, neuloží žádné změny
        verbose (bool): Pokud True, vypisuje podrobnější informace
    """
    start_time = datetime.now()
    logging.info(f"Spouštím univerzální opravu všech galerií (dry_run={dry_run}, verbose={verbose})...")
    
    try:
        # Připojení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        image_map = load_mapping('image_map.json')
        article_map = load_mapping('article_map.json')
        gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
        
        if not image_map:
            logging.error("Chybí mapování obrázků. Nejprve spusťte migraci obrázků.")
            return
            
        if not article_map:
            logging.error("Chybí mapování článků. Nejprve spusťte migraci článků.")
            return
        
        # 1. STRATEGIE: Oprava na základě dat v PostgreSQL
        logging.info("STRATEGIE 1: Oprava článků s více obrázky v PostgreSQL...")
        
        # Získání článků s více než jedním obrázkem v PostgreSQL
        pg_cursor.execute(f"""
            SELECT polozka_id, COUNT(*) 
            FROM {sabre_prefix}obrazek 
            WHERE active_state = 1 AND typ = 1 
            GROUP BY polozka_id 
            HAVING COUNT(*) > 1
        """)
        
        articles_with_multiple_images = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles_with_multiple_images)} článků s více obrázky v PostgreSQL.")
        
        # Zjistit ID článků v postgresql
        unique_ids = [row[0] for row in articles_with_multiple_images]
        
        # Získat články podle unique_id
        articles_info = {}
        
        if unique_ids:
            placeholders = ','.join(['%s'] * len(unique_ids))
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, nazev 
                FROM {sabre_prefix}clanek 
                WHERE unikatni_id IN ({placeholders})
            """, unique_ids)
            
            for id_clanek, unikatni_id, nazev in pg_cursor.fetchall():
                articles_info[unikatni_id] = (id_clanek, nazev)
        
        logging.info(f"Nalezeno {len(articles_info)} článků s odpovídajícími unique_id v PostgreSQL.")
        
        # Statistiky
        strategy1_updated = 0
        strategy1_failed = 0
        strategy1_skipped = 0
        
        # Pro každý článek s více obrázky
        for unique_id, count in articles_with_multiple_images:
            try:
                # Zjistit, zda máme informace o článku
                if unique_id not in articles_info:
                    if verbose:
                        logging.warning(f"Pro unique_id {unique_id} nebyl nalezen článek v PostgreSQL.")
                    strategy1_skipped += 1
                    continue
                    
                article_id, article_title = articles_info[unique_id]
                
                # Zkontrolovat, zda je článek v mapování
                if str(article_id) not in article_map:
                    if verbose:
                        logging.warning(f"Článek {article_id} ({article_title}) není v mapování.")
                    strategy1_skipped += 1
                    continue
                
                wp_article_id = article_map[str(article_id)]
                if verbose:
                    logging.info(f"Zpracovávám článek {article_id} ({article_title}) - WordPress ID: {wp_article_id}")
                
                # Získat všechny obrázky článku z PostgreSQL
                pg_cursor.execute(f"""
                    SELECT id_obrazek, soubor, priorita, active_state
                    FROM {sabre_prefix}obrazek 
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek
                """, (unique_id,))
                
                obrazky = pg_cursor.fetchall()
                if verbose:
                    logging.info(f"Nalezeno {len(obrazky)} aktivních obrázků typu 1 v PostgreSQL.")
                
                # Najít odpovídající WordPress ID obrázků pomocí mapování
                wp_image_ids = find_wp_image_ids(unique_id, obrazky, image_map, mysql_conn, wp_prefix)
                
                # Pokud nemáme žádné obrázky, přeskočit
                if not wp_image_ids:
                    logging.warning(f"Pro článek {article_id} ({article_title}) nebyly nalezeny žádné obrázky ve WordPress.")
                    strategy1_skipped += 1
                    continue
                
                if verbose:
                    logging.info(f"Nalezeno {len(wp_image_ids)} obrázků ve WordPress pro článek {article_id}.")
                
                # Zkontrolovat, zda článek má metadata sabre_gallery
                mysql_cursor.execute(f"""
                    SELECT meta_id, meta_value 
                    FROM {wp_prefix}postmeta 
                    WHERE post_id = %s AND meta_key = 'sabre_gallery'
                """, (wp_article_id,))
                
                gallery_meta = mysql_cursor.fetchone()
                
                if not gallery_meta:
                    if verbose:
                        logging.info(f"Článek {wp_article_id} nemá žádná metadata sabre_gallery, vytvářím nová.")
                    
                    # Vytvořit nová metadata
                    meta_value = {
                        'original_article_id': article_id,
                        'original_unique_id': unique_id,
                        'article_title': article_title,
                        'image_ids': wp_image_ids,
                        'image_count': len(wp_image_ids),
                        'fixed_migration': True,
                        'migration_timestamp': datetime.now().isoformat()
                    }
                    
                    if not dry_run:
                        mysql_cursor.execute(f"""
                            INSERT INTO {wp_prefix}postmeta (post_id, meta_key, meta_value)
                            VALUES (%s, %s, %s)
                        """, (wp_article_id, 'sabre_gallery', json.dumps(meta_value)))
                else:
                    meta_id, meta_value_json = gallery_meta['meta_id'], gallery_meta['meta_value']
                    
                    # Dekódovat JSON
                    try:
                        gallery_data = json.loads(meta_value_json)
                    except json.JSONDecodeError:
                        logging.error(f"Neplatný JSON v metadatech pro post_id {wp_article_id}")
                        strategy1_failed += 1
                        continue
                    
                    # Porovnat počet obrázků
                    original_image_count = len(gallery_data.get('image_ids', []))
                    
                    if original_image_count >= len(wp_image_ids):
                        if verbose:
                            logging.info(f"Článek {wp_article_id} již má {original_image_count} obrázků, není potřeba opravovat.")
                        strategy1_skipped += 1
                        continue
                    
                    # Aktualizovat metadata
                    gallery_data['image_ids'] = wp_image_ids
                    gallery_data['image_count'] = len(wp_image_ids)
                    gallery_data['fixed_migration'] = True
                    gallery_data['migration_timestamp'] = datetime.now().isoformat()
                    
                    if not dry_run:
                        mysql_cursor.execute(f"""
                            UPDATE {wp_prefix}postmeta 
                            SET meta_value = %s 
                            WHERE meta_id = %s
                        """, (json.dumps(gallery_data), meta_id))
                
                # Aktualizovat shortcode v obsahu článku
                mysql_cursor.execute(f"""
                    SELECT ID, post_content 
                    FROM {wp_prefix}posts 
                    WHERE ID = %s
                """, (wp_article_id,))
                
                post_data = mysql_cursor.fetchone()
                
                if not post_data:
                    logging.error(f"Článek s ID {wp_article_id} nebyl nalezen ve WordPress.")
                    strategy1_failed += 1
                    continue
                
                post_content = post_data['post_content']
                
                # Najít všechny gallery shortcodes
                gallery_shortcodes = re.findall(r'\[gallery[^\]]*\]', post_content)
                
                # Připravit nový shortcode
                new_gallery_shortcode = f'[gallery ids="{",".join(wp_image_ids)}"]'
                
                if gallery_shortcodes:
                    # Nahradit všechny staré shortcodes
                    for old_shortcode in gallery_shortcodes:
                        post_content = post_content.replace(old_shortcode, new_gallery_shortcode)
                else:
                    # Shortcode neexistuje, přidat ho na konec obsahu
                    post_content += "\n\n" + new_gallery_shortcode
                
                # Aktualizovat obsah článku
                if not dry_run:
                    mysql_cursor.execute(f"""
                        UPDATE {wp_prefix}posts 
                        SET post_content = %s 
                        WHERE ID = %s
                    """, (post_content, wp_article_id))
                
                # Aktualizovat gallery_map
                gallery_key = f"article_{article_id}"
                gallery_map[gallery_key] = {
                    'wp_post_id': wp_article_id,
                    'gallery_shortcode': new_gallery_shortcode,
                    'image_count': len(wp_image_ids),
                    'fixed': True,
                    'timestamp': datetime.now().isoformat()
                }
                
                if not dry_run:
                    # Commitnout změny
                    mysql_conn.commit()
                
                logging.info(f"Článek {wp_article_id} ({article_title}) úspěšně opraven - {len(wp_image_ids)} obrázků.")
                strategy1_updated += 1
                
            except Exception as e:
                if not dry_run:
                    mysql_conn.rollback()
                logging.error(f"Chyba při opravě článku: {e}")
                if verbose:
                    import traceback
                    logging.error(traceback.format_exc())
                strategy1_failed += 1
        
        # 2. STRATEGIE: Kontrola WordPress metadat sabre_gallery
        logging.info("STRATEGIE 2: Kontrola WordPress metadat sabre_gallery...")
        
        # Získat všechny záznamy s metadaty sabre_gallery
        mysql_cursor.execute(f"""
            SELECT post_id, meta_id, meta_value
            FROM {wp_prefix}postmeta
            WHERE meta_key = 'sabre_gallery'
        """)
        
        galleries = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(galleries)} záznamů s metadaty sabre_gallery.")
        
        # Statistiky
        strategy2_updated = 0
        strategy2_failed = 0
        strategy2_skipped = 0
        
        # Pro každý záznam
        for gallery in galleries:
            try:
                post_id = gallery['post_id']
                meta_id = gallery['meta_id']
                meta_value = gallery['meta_value']
                
                # Dekódovat JSON metadata
                try:
                    gallery_data = json.loads(meta_value)
                except json.JSONDecodeError:
                    logging.error(f"Neplatný JSON v metadatech pro post_id {post_id}")
                    strategy2_failed += 1
                    continue
                
                # Původní počet obrázků
                original_image_count = len(gallery_data.get('image_ids', []))
                
                # Přeskočit články, které již byly opraveny strategií 1
                gallery_key = f"article_{gallery_data.get('original_article_id', '')}"
                if gallery_key in gallery_map and gallery_map[gallery_key].get('fixed'):
                    if verbose:
                        logging.info(f"Post ID {post_id}: Již opraven strategií 1, přeskakuji.")
                    strategy2_skipped += 1
                    continue
                
                # Pokud nemáme original_unique_id, nemůžeme opravit
                if 'original_unique_id' not in gallery_data:
                    if verbose:
                        logging.warning(f"Post ID {post_id}: Chybí original_unique_id, nelze opravit")
                    strategy2_skipped += 1
                    continue
                
                unique_id = gallery_data['original_unique_id']
                article_title = gallery_data.get('article_title', 'Bez názvu')
                
                # Zkontrolovat, zda jsou v DB dostupné data
                mysql_cursor.execute(f"""
                    SELECT post_content
                    FROM {wp_prefix}posts
                    WHERE ID = %s
                """, (post_id,))
                
                post_result = mysql_cursor.fetchone()
                if not post_result:
                    logging.warning(f"Post ID {post_id} neexistuje v databázi")
                    strategy2_failed += 1
                    continue
                
                post_content = post_result['post_content']
                
                # Získat všechny obrázky s tímto unique_id z PostgreSQL
                pg_cursor.execute(f"""
                    SELECT id_obrazek, soubor, priorita, active_state 
                    FROM {sabre_prefix}obrazek 
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek
                """, (unique_id,))
                
                obrazky = pg_cursor.fetchall()
                
                if not obrazky:
                    if verbose:
                        logging.warning(f"Pro unique_id {unique_id} nebyly nalezeny žádné obrázky v PostgreSQL.")
                    strategy2_skipped += 1
                    continue
                
                # Získat článek v PostgreSQL
                pg_cursor.execute(f"""
                    SELECT id_clanek, nazev 
                    FROM {sabre_prefix}clanek 
                    WHERE unikatni_id = %s
                """, (unique_id,))
                
                article_data = pg_cursor.fetchone()
                
                if not article_data:
                    if verbose:
                        logging.warning(f"Pro unique_id {unique_id} nebyl nalezen článek v PostgreSQL.")
                    strategy2_skipped += 1
                    continue
                
                article_id, article_title = article_data
                
                if verbose:
                    logging.info(f"Post ID {post_id} ({article_title}): Nalezeno {len(obrazky)} obrázků v PostgreSQL.")
                
                # Najít WordPress ID obrázků
                wp_image_ids = find_wp_image_ids(unique_id, obrazky, image_map, mysql_conn, wp_prefix)
                
                # Pokud nemáme žádné nové obrázky, zkusíme použít původní image_ids
                if not wp_image_ids and 'image_ids' in gallery_data and gallery_data['image_ids']:
                    wp_image_ids = gallery_data['image_ids']
                
                # Pokud stále nemáme žádné obrázky, nemůžeme opravit
                if not wp_image_ids:
                    logging.warning(f"Post ID {post_id} ({article_title}): Nenalezeny žádné obrázky k přiřazení")
                    strategy2_failed += 1
                    continue
                
                # Podívat se na současný počet obrázků a porovnat s novým počtem
                if original_image_count >= len(wp_image_ids):
                    if verbose:
                        logging.info(f"Post ID {post_id} ({article_title}): Počet obrázků je již {original_image_count}, není potřeba opravovat")
                    strategy2_skipped += 1
                    continue
                
                # Aktualizovat metadata
                gallery_data['image_ids'] = wp_image_ids
                gallery_data['image_count'] = len(wp_image_ids)
                gallery_data['fixed_migration'] = True
                gallery_data['migration_timestamp'] = datetime.now().isoformat()
                
                # Aktualizovat databázi
                if not dry_run:
                    mysql_cursor.execute(f"""
                        UPDATE {wp_prefix}postmeta
                        SET meta_value = %s
                        WHERE meta_id = %s
                    """, (json.dumps(gallery_data), meta_id))
                
                # Aktualizovat gallery shortcode v obsahu příspěvku
                gallery_shortcodes = re.findall(r'\[gallery[^\]]*\]', post_content)
                new_gallery_shortcode = f'[gallery ids="{",".join(wp_image_ids)}"]'
                
                if gallery_shortcodes:
                    # Nahradit všechny gallery shortcodes novým shortcodem
                    for old_shortcode in gallery_shortcodes:
                        post_content = post_content.replace(old_shortcode, new_gallery_shortcode)
                else:
                    # Přidat shortcode na konec obsahu
                    post_content += "\n\n" + new_gallery_shortcode
                
                # Aktualizovat obsah příspěvku
                if not dry_run:
                    mysql_cursor.execute(f"""
                        UPDATE {wp_prefix}posts
                        SET post_content = %s
                        WHERE ID = %s
                    """, (post_content, post_id))
                
                # Aktualizovat gallery_map
                gallery_key = f"article_{article_id}"
                gallery_map[gallery_key] = {
                    'wp_post_id': post_id,
                    'gallery_shortcode': new_gallery_shortcode,
                    'image_count': len(wp_image_ids),
                    'fixed': True,
                    'timestamp': datetime.now().isoformat()
                }
                
                if not dry_run:
                    # Commituji změny
                    mysql_conn.commit()
                
                logging.info(f"Post ID {post_id} ({article_title}): Aktualizováno z {original_image_count} na {len(wp_image_ids)} obrázků")
                strategy2_updated += 1
                
            except Exception as e:
                if not dry_run:
                    mysql_conn.rollback()
                logging.error(f"Chyba při opravě galerie pro post_id {gallery['post_id']}: {e}")
                if verbose:
                    import traceback
                    logging.error(traceback.format_exc())
                strategy2_failed += 1
        
        # Uložit aktualizované mapování galerií
        if not dry_run:
            save_mapping(gallery_map, 'gallery_map.json')
        
        # Finální výpis statistik
        logging.info("-" * 50)
        logging.info("SOUHRN OPRAVY GALERIÍ:")
        logging.info(f"STRATEGIE 1 (PostgreSQL data):")
        logging.info(f"  - Opraveno: {strategy1_updated}")
        logging.info(f"  - Přeskočeno: {strategy1_skipped}")
        logging.info(f"  - Selhalo: {strategy1_failed}")
        logging.info(f"STRATEGIE 2 (WordPress metadata):")
        logging.info(f"  - Opraveno: {strategy2_updated}")
        logging.info(f"  - Přeskočeno: {strategy2_skipped}")
        logging.info(f"  - Selhalo: {strategy2_failed}")
        logging.info(f"CELKEM:")
        logging.info(f"  - Opraveno: {strategy1_updated + strategy2_updated}")
        logging.info(f"  - Přeskočeno: {strategy1_skipped + strategy2_skipped}")
        logging.info(f"  - Selhalo: {strategy1_failed + strategy2_failed}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        logging.info(f"Oprava dokončena za {duration.total_seconds():.2f} sekund.")
        
        return strategy1_updated + strategy2_updated
        
    except Exception as e:
        logging.error(f"Obecná chyba při opravě galerií: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return 0
    finally:
        # Zavřít všechna spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    # Zpracování parametrů příkazové řádky
    parser = argparse.ArgumentParser(description='Univerzální oprava galerií pro všechny články.')
    parser.add_argument('--dry-run', action='store_true', help='Pouze simulovat opravy bez ukládání')
    parser.add_argument('--verbose', action='store_true', help='Podrobný výpis průběhu opravy')
    parser.add_argument('--log-file', help='Vlastní cesta k log souboru')
    args = parser.parse_args()
    
    # Nastavení vlastního log souboru, pokud je zadán
    if args.log_file:
        log_file = args.log_file
        # Přenastavit logging
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(args.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    # Spustit opravu
    updated_count = fix_all_galleries(dry_run=args.dry_run, verbose=args.verbose)
    
    # Výstupní kód podle počtu opravených článků
    sys.exit(0 if updated_count > 0 or args.dry_run else 1)
