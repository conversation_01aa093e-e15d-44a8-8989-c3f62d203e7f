#!/usr/bin/env python3
"""
Test Migration Steps Script
===========================

This script allows testing individual migration steps and validating
the complete migration pipeline without running the full reset.

Usage:
    python test_migration_steps.py --check-status
    python test_migration_steps.py --validate-images
    python test_migration_steps.py --check-duplicates
    python test_migration_steps.py --test-connections
"""

import argparse
import json
import logging
import mysql.connector
import psycopg2
from pathlib import Path

from config import (
    MYSQL_CONFIG, PG_CONFIG, MAPPINGS_DIR,
    TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_WP_USERS, TBL_WP_TERMS,
    TBL_CLANEK, TBL_OBRAZEK, TBL_RUBRIKA
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class MigrationTester:
    def __init__(self):
        self.mysql_conn = None
        self.pg_conn = None
    
    def connect_databases(self):
        """Connect to both databases"""
        try:
            self.mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
            self.pg_conn = psycopg2.connect(**PG_CONFIG)
            logging.info("✅ Database connections established")
            return True
        except Exception as e:
            logging.error(f"❌ Database connection failed: {e}")
            return False
    
    def close_connections(self):
        """Close database connections"""
        if self.mysql_conn:
            self.mysql_conn.close()
        if self.pg_conn:
            self.pg_conn.close()
    
    def check_migration_status(self):
        """Check current migration status"""
        if not self.connect_databases():
            return False
        
        try:
            mysql_cursor = self.mysql_conn.cursor()
            pg_cursor = self.pg_conn.cursor()
            
            print("\n" + "="*60)
            print("MIGRATION STATUS REPORT")
            print("="*60)
            
            # PostgreSQL source counts
            print("\nSOURCE DATABASE (PostgreSQL):")
            
            pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_CLANEK}")
            pg_articles = pg_cursor.fetchone()[0]
            print(f"  Articles: {pg_articles:,}")
            
            pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_OBRAZEK}")
            pg_images = pg_cursor.fetchone()[0]
            print(f"  Images: {pg_images:,}")
            
            pg_cursor.execute(f"SELECT COUNT(DISTINCT autor) FROM {TBL_CLANEK} WHERE autor IS NOT NULL AND autor != ''")
            pg_users = pg_cursor.fetchone()[0]
            print(f"  Users (unique authors): {pg_users:,}")
            
            pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_RUBRIKA}")
            pg_categories = pg_cursor.fetchone()[0]
            print(f"  Categories: {pg_categories:,}")
            
            # WordPress target counts
            print("\nTARGET DATABASE (WordPress/MySQL):")
            
            mysql_cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_POSTS} WHERE post_type = 'post'")
            wp_articles = mysql_cursor.fetchone()[0]
            print(f"  Articles: {wp_articles:,}")
            
            mysql_cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_POSTS} WHERE post_type = 'attachment'")
            wp_images = mysql_cursor.fetchone()[0]
            print(f"  Images: {wp_images:,}")
            
            mysql_cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_USERS} WHERE ID > 1")
            wp_users = mysql_cursor.fetchone()[0]
            print(f"  Users: {wp_users:,}")
            
            mysql_cursor.execute(f"SELECT COUNT(*) FROM {TBL_WP_TERMS} WHERE term_id > 1")
            wp_categories = mysql_cursor.fetchone()[0]
            print(f"  Categories: {wp_categories:,}")
            
            # Migration percentages
            print("\nMIGRATION PROGRESS:")
            print(f"  Articles: {wp_articles:,} / {pg_articles:,} ({wp_articles/pg_articles*100:.1f}%)")
            print(f"  Images: {wp_images:,} / {pg_images:,} ({wp_images/pg_images*100:.1f}%)")
            print(f"  Users: {wp_users:,} / {pg_users:,} ({wp_users/pg_users*100:.1f}%)")
            print(f"  Categories: {wp_categories:,} / {pg_categories:,} ({wp_categories/pg_categories*100:.1f}%)")
            
            # Check mapping files
            print("\nMAPPING FILES:")
            mapping_files = ['image_map.json', 'article_map.json', 'category_map.json', 'user_map.json']
            for mapping_file in mapping_files:
                file_path = Path(MAPPINGS_DIR) / mapping_file
                if file_path.exists():
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    print(f"  {mapping_file}: {len(data):,} entries")
                else:
                    print(f"  {mapping_file}: ❌ Missing")
            
            mysql_cursor.close()
            pg_cursor.close()
            return True
            
        except Exception as e:
            logging.error(f"Status check failed: {e}")
            return False
        finally:
            self.close_connections()
    
    def validate_images(self):
        """Validate image migration and content"""
        if not self.connect_databases():
            return False
        
        try:
            mysql_cursor = self.mysql_conn.cursor()
            
            print("\n" + "="*60)
            print("IMAGE VALIDATION REPORT")
            print("="*60)
            
            # Check for old image URLs in content
            mysql_cursor.execute(f"""
                SELECT COUNT(*) FROM {TBL_WP_POSTS} 
                WHERE post_content LIKE '%/obrazek/%'
            """)
            old_urls = mysql_cursor.fetchone()[0]
            print(f"\nOld image URLs in content: {old_urls}")
            
            if old_urls > 0:
                print("❌ Some articles still contain old image URLs")
                
                # Show examples
                mysql_cursor.execute(f"""
                    SELECT ID, post_title FROM {TBL_WP_POSTS} 
                    WHERE post_content LIKE '%/obrazek/%'
                    LIMIT 5
                """)
                examples = mysql_cursor.fetchall()
                print("Examples:")
                for post_id, title in examples:
                    print(f"  - ID {post_id}: {title[:50]}...")
            else:
                print("✅ No old image URLs found in content")
            
            # Check featured images
            mysql_cursor.execute(f"""
                SELECT COUNT(*) FROM {TBL_WP_POSTMETA} 
                WHERE meta_key = '_thumbnail_id'
            """)
            featured_count = mysql_cursor.fetchone()[0]
            print(f"\nArticles with featured images: {featured_count:,}")
            
            # Check for duplicate featured images
            mysql_cursor.execute(f"""
                SELECT meta_value, COUNT(*) as count
                FROM {TBL_WP_POSTMETA}
                WHERE meta_key = '_thumbnail_id'
                GROUP BY meta_value
                HAVING COUNT(*) > 10
                ORDER BY count DESC
                LIMIT 5
            """)
            duplicates = mysql_cursor.fetchall()
            
            if duplicates:
                print("\nMost common featured images (potential duplicates):")
                for image_id, count in duplicates:
                    print(f"  Image ID {image_id}: {count} articles")
            
            mysql_cursor.close()
            return True
            
        except Exception as e:
            logging.error(f"Image validation failed: {e}")
            return False
        finally:
            self.close_connections()
    
    def check_duplicates(self):
        """Check for duplicate content and potential issues"""
        if not self.connect_databases():
            return False
        
        try:
            mysql_cursor = self.mysql_conn.cursor()
            
            print("\n" + "="*60)
            print("DUPLICATE CONTENT REPORT")
            print("="*60)
            
            # Check for duplicate post titles
            mysql_cursor.execute(f"""
                SELECT post_title, COUNT(*) as count
                FROM {TBL_WP_POSTS}
                WHERE post_type = 'post'
                GROUP BY post_title
                HAVING COUNT(*) > 1
                ORDER BY count DESC
                LIMIT 10
            """)
            duplicate_titles = mysql_cursor.fetchall()
            
            if duplicate_titles:
                print(f"\nDuplicate article titles ({len(duplicate_titles)}):")
                for title, count in duplicate_titles:
                    print(f"  '{title[:50]}...': {count} articles")
            else:
                print("\n✅ No duplicate article titles found")
            
            # Check for duplicate usernames
            mysql_cursor.execute(f"""
                SELECT user_login, COUNT(*) as count
                FROM {TBL_WP_USERS}
                GROUP BY user_login
                HAVING COUNT(*) > 1
                ORDER BY count DESC
            """)
            duplicate_users = mysql_cursor.fetchall()
            
            if duplicate_users:
                print(f"\nDuplicate usernames ({len(duplicate_users)}):")
                for username, count in duplicate_users:
                    print(f"  '{username}': {count} users")
            else:
                print("\n✅ No duplicate usernames found")
            
            # Check for orphaned metadata
            mysql_cursor.execute(f"""
                SELECT COUNT(*) FROM {TBL_WP_POSTMETA} pm
                LEFT JOIN {TBL_WP_POSTS} p ON pm.post_id = p.ID
                WHERE p.ID IS NULL
            """)
            orphaned_meta = mysql_cursor.fetchone()[0]
            
            if orphaned_meta > 0:
                print(f"\n❌ Orphaned post metadata: {orphaned_meta} records")
            else:
                print("\n✅ No orphaned post metadata found")
            
            mysql_cursor.close()
            return True
            
        except Exception as e:
            logging.error(f"Duplicate check failed: {e}")
            return False
        finally:
            self.close_connections()
    
    def test_connections(self):
        """Test database connections and basic queries"""
        print("\n" + "="*60)
        print("CONNECTION TEST REPORT")
        print("="*60)
        
        # Test PostgreSQL
        try:
            pg_conn = psycopg2.connect(**PG_CONFIG)
            pg_cursor = pg_conn.cursor()
            pg_cursor.execute("SELECT version()")
            pg_version = pg_cursor.fetchone()[0]
            print(f"\n✅ PostgreSQL connection successful")
            print(f"   Version: {pg_version}")
            pg_cursor.close()
            pg_conn.close()
        except Exception as e:
            print(f"\n❌ PostgreSQL connection failed: {e}")
            return False
        
        # Test MySQL
        try:
            mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
            mysql_cursor = mysql_conn.cursor()
            mysql_cursor.execute("SELECT VERSION()")
            mysql_version = mysql_cursor.fetchone()[0]
            print(f"\n✅ MySQL connection successful")
            print(f"   Version: {mysql_version}")
            mysql_cursor.close()
            mysql_conn.close()
        except Exception as e:
            print(f"\n❌ MySQL connection failed: {e}")
            return False
        
        print("\n✅ All database connections working properly")
        return True


def main():
    parser = argparse.ArgumentParser(description="Test Migration Steps")
    
    parser.add_argument('--check-status', action='store_true', help='Check migration status')
    parser.add_argument('--validate-images', action='store_true', help='Validate image migration')
    parser.add_argument('--check-duplicates', action='store_true', help='Check for duplicates')
    parser.add_argument('--test-connections', action='store_true', help='Test database connections')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    
    args = parser.parse_args()
    
    if not any([args.check_status, args.validate_images, args.check_duplicates, args.test_connections, args.all]):
        parser.print_help()
        return
    
    tester = MigrationTester()
    
    if args.all or args.test_connections:
        tester.test_connections()
    
    if args.all or args.check_status:
        tester.check_migration_status()
    
    if args.all or args.validate_images:
        tester.validate_images()
    
    if args.all or args.check_duplicates:
        tester.check_duplicates()


if __name__ == "__main__":
    main()
