#!/usr/bin/env python3
import logging
import os
import json
import sys
import mysql.connector
import psycopg2
from datetime import datetime
import re

# Nastavení logování
LOG_FILE = os.path.join('logs', f'fix_gallery_images_postgres_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_pg_connection():
    """
    Vytvoří a vrátí spojení s PostgreSQL databází pro SABRE
    na základě konfigurace v .env souboru
    """
    # Načtení konfigurace z .env souboru
    pg_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('PG_'):
                    pg_config[key[3:].lower()] = value

    return psycopg2.connect(
        host=pg_config.get('host', 'localhost'),
        user=pg_config.get('user', ''),
        password=pg_config.get('password', ''),
        database=pg_config.get('dbname', ''),
        port=int(pg_config.get('port', 5432))
    )

def get_mysql_connection():
    """
    Vytvoří a vrátí spojení s MySQL databází pro WordPress 
    na základě konfigurace v .env souboru
    """
    # Načtení konfigurace z .env souboru
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    sabre_prefix = 'prefix_'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
                elif line.startswith('SABRE_TABLE_PREFIX='):
                    sabre_prefix = line.split('=', 1)[1]
    
    return wp_prefix, sabre_prefix

def fix_gallery_entries_with_postgres():
    """
    Opraví záznamy galerií přímo s použitím dat z PostgreSQL databáze.
    """
    logging.info("Spouštím opravu galerií s chybějícími obrázky pomocí PostgreSQL...")
    
    try:
        # Připojení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        image_map = load_mapping('image_map.json')
        article_map = load_mapping('article_map.json')
        
        if not image_map:
            logging.error("Chybí mapování obrázků. Nejprve spusťte migraci obrázků.")
            return
            
        if not article_map:
            logging.error("Chybí mapování článků. Nejprve spusťte migraci článků.")
            return
        
        # Získání článků s více než jedním obrázkem v PostgreSQL
        pg_cursor.execute(f"""
            SELECT polozka_id, COUNT(*) 
            FROM {sabre_prefix}obrazek 
            WHERE active_state = 1 AND typ = 1 
            GROUP BY polozka_id 
            HAVING COUNT(*) > 1
        """)
        
        articles_with_multiple_images = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles_with_multiple_images)} článků s více obrázky v PostgreSQL.")
        
        # Zjistit ID článků v postgresql
        unique_ids = [row[0] for row in articles_with_multiple_images]
        
        # Získat články podle unique_id
        placeholders = ','.join(['%s'] * len(unique_ids))
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev 
            FROM {sabre_prefix}clanek 
            WHERE unikatni_id IN ({placeholders})
        """, unique_ids)
        
        article_info = {}
        for id_clanek, unikatni_id, nazev in pg_cursor.fetchall():
            article_info[unikatni_id] = (id_clanek, nazev)
        
        logging.info(f"Nalezeno {len(article_info)} článků s odpovídajícími unique_id v PostgreSQL.")
        
        # Statistiky
        updated_count = 0
        failed_count = 0
        skipped_count = 0
        
        # Pro každý článek s více obrázky
        for unique_id, count in articles_with_multiple_images:
            try:
                # Zjistit, zda máme informace o článku
                if unique_id not in article_info:
                    logging.warning(f"Pro unique_id {unique_id} nebyl nalezen článek v PostgreSQL.")
                    skipped_count += 1
                    continue
                    
                article_id, article_title = article_info[unique_id]
                
                # Zkontrolovat, zda je článek v mapování
                if str(article_id) not in article_map:
                    logging.warning(f"Článek {article_id} ({article_title}) není v mapování.")
                    skipped_count += 1
                    continue
                
                wp_article_id = article_map[str(article_id)]
                logging.info(f"Zpracovávám článek {article_id} ({article_title}) - WordPress ID: {wp_article_id}")
                
                # Získat všechny obrázky článku z PostgreSQL
                pg_cursor.execute(f"""
                    SELECT id_obrazek, soubor, priorita 
                    FROM {sabre_prefix}obrazek 
                    WHERE polozka_id = %s AND active_state = 1 AND typ = 1
                    ORDER BY priorita DESC, id_obrazek
                """, (unique_id,))
                
                obrazky = pg_cursor.fetchall()
                logging.info(f"Nalezeno {len(obrazky)} aktivních obrázků typu 1 v PostgreSQL.")
                
                # Najít odpovídající WordPress ID obrázků pomocí mapování
                wp_image_ids = []
                
                for _, img_file, _ in obrazky:
                    # Zkusit najít v mapování přímo
                    if img_file in image_map:
                        wp_id = image_map[img_file].get('wp_id')
                        if wp_id and str(wp_id) not in wp_image_ids:
                            wp_image_ids.append(str(wp_id))
                    else:
                        # Zkusit najít podle základního názvu v image_map
                        base_name, ext = os.path.splitext(img_file)
                        found = False
                        for map_key, map_data in image_map.items():
                            if map_key.startswith(base_name) or os.path.basename(map_key).startswith(base_name):
                                wp_id = map_data.get('wp_id')
                                if wp_id and str(wp_id) not in wp_image_ids:
                                    wp_image_ids.append(str(wp_id))
                                    found = True
                                    break
                        
                        if not found:
                            # Zkusit najít v celém unique_id adresáři
                            img_path = f"{unique_id}/{img_file}"
                            if img_path in image_map:
                                wp_id = image_map[img_path].get('wp_id')
                                if wp_id and str(wp_id) not in wp_image_ids:
                                    wp_image_ids.append(str(wp_id))
                            else:
                                logging.warning(f"Pro obrázek {img_file} nebyl nalezen záznam v mapování.")
                
                # Pokud nemáme žádné obrázky, přeskočit
                if not wp_image_ids:
                    logging.warning(f"Pro článek {article_id} ({article_title}) nebyly nalezeny žádné obrázky ve WordPress.")
                    skipped_count += 1
                    continue
                
                logging.info(f"Nalezeno {len(wp_image_ids)} obrázků ve WordPress pro článek {article_id}.")
                
                # Zkontrolovat, zda článek má metadata sabre_gallery
                mysql_cursor.execute(f"""
                    SELECT meta_id, meta_value 
                    FROM {wp_prefix}postmeta 
                    WHERE post_id = %s AND meta_key = 'sabre_gallery'
                """, (wp_article_id,))
                
                gallery_meta = mysql_cursor.fetchone()
                
                if not gallery_meta:
                    logging.warning(f"Článek {wp_article_id} nemá žádná metadata sabre_gallery.")
                    
                    # Vytvořit nová metadata
                    meta_value = {
                        'original_article_id': article_id,
                        'original_unique_id': unique_id,
                        'article_title': article_title,
                        'image_ids': wp_image_ids,
                        'image_count': len(wp_image_ids),
                        'fixed_migration': True,
                        'migration_timestamp': datetime.now().isoformat()
                    }
                    
                    mysql_cursor.execute(f"""
                        INSERT INTO {wp_prefix}postmeta (post_id, meta_key, meta_value)
                        VALUES (%s, %s, %s)
                    """, (wp_article_id, 'sabre_gallery', json.dumps(meta_value)))
                else:
                    meta_id, meta_value_json = gallery_meta
                    
                    # Dekódovat JSON
                    try:
                        gallery_data = json.loads(meta_value_json)
                    except json.JSONDecodeError:
                        logging.error(f"Neplatný JSON v metadatech pro post_id {wp_article_id}")
                        failed_count += 1
                        continue
                    
                    # Porovnat počet obrázků
                    original_image_count = len(gallery_data.get('image_ids', []))
                    
                    if original_image_count >= len(wp_image_ids):
                        logging.info(f"Článek {wp_article_id} již má {original_image_count} obrázků, není potřeba opravovat.")
                        skipped_count += 1
                        continue
                    
                    # Aktualizovat metadata
                    gallery_data['image_ids'] = wp_image_ids
                    gallery_data['image_count'] = len(wp_image_ids)
                    gallery_data['fixed_migration'] = True
                    gallery_data['migration_timestamp'] = datetime.now().isoformat()
                    
                    mysql_cursor.execute(f"""
                        UPDATE {wp_prefix}postmeta 
                        SET meta_value = %s 
                        WHERE meta_id = %s
                    """, (json.dumps(gallery_data), meta_id))
                
                # Aktualizovat shortcode v obsahu článku
                mysql_cursor.execute(f"""
                    SELECT ID, post_content 
                    FROM {wp_prefix}posts 
                    WHERE ID = %s
                """, (wp_article_id,))
                
                post_data = mysql_cursor.fetchone()
                
                if not post_data:
                    logging.error(f"Článek s ID {wp_article_id} nebyl nalezen ve WordPress.")
                    failed_count += 1
                    continue
                
                post_content = post_data['post_content']
                
                # Najít všechny gallery shortcodes
                gallery_shortcodes = re.findall(r'\[gallery[^\]]*\]', post_content)
                
                if gallery_shortcodes:
                    # Připravit nový shortcode
                    new_gallery_shortcode = f'[gallery ids="{",".join(wp_image_ids)}"]'
                    
                    # Nahradit všechny staré shortcodes
                    for old_shortcode in gallery_shortcodes:
                        post_content = post_content.replace(old_shortcode, new_gallery_shortcode)
                else:
                    # Shortcode neexistuje, přidat ho na konec obsahu
                    new_gallery_shortcode = f'[gallery ids="{",".join(wp_image_ids)}"]'
                    post_content += "\n\n" + new_gallery_shortcode
                
                # Aktualizovat obsah článku
                mysql_cursor.execute(f"""
                    UPDATE {wp_prefix}posts 
                    SET post_content = %s 
                    WHERE ID = %s
                """, (post_content, wp_article_id))
                
                # Commitnout změny
                mysql_conn.commit()
                
                logging.info(f"Článek {wp_article_id} ({article_title}) úspěšně opraven.")
                updated_count += 1
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při opravě článku: {e}")
                import traceback
                logging.error(traceback.format_exc())
                failed_count += 1
        
        # Finální výpis
        logging.info("Oprava galerií dokončena.")
        logging.info(f"Celkem aktualizováno: {updated_count}")
        logging.info(f"Přeskočeno: {skipped_count}")
        logging.info(f"Selhalo: {failed_count}")
        
    except Exception as e:
        logging.error(f"Obecná chyba: {e}")
        import traceback
        logging.error(traceback.format_exc())
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

if __name__ == "__main__":
    fix_gallery_entries_with_postgres()
