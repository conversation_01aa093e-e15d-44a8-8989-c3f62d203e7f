# Oprava Cest k Obrázkům v Obsahu po Migraci

Tento dokument popisuje nástroj pro rozšířenou opravu cest k obrázkům v obsahu článků po migraci z SABRE do WordPress.

## 🔍 Problém

Po migraci se v obsahu článků mohou vyskytnout následující problémy s cestami k obrázkům:

1. **Neopravené cesty** - odka<PERSON> na původní umístění obrázků
2. **Nekonzistentní formáty** - různé varianty zápisu cest (relativní vs. absolutní)
3. **Nekompletní mapování** - některé obrázky nebyly správně namapovány mezi SABRE a WordPress
4. **Nestandardní HTML** - tagy img s nestandardním formátováním nebo atributy

## 🚀 Řešení

Nový skript `fix_content_image_paths.py` implementuje rozšířenou opravu cest k obrázkům s následujícími vylepšeními:

1. **Rozšířená detekce** - více regex vzorů pro zachycení všech variant cest k obrázkům:
   - Standardní HTML tag `<img src="...">`
   - Tag s nestandardními mezerami nebo zalomením řádků
   - Atribut `data-src` používaný pro lazy-loading
   - CSS styly s `background-image: url(...)`
   - Inline styly s background-image

2. **Normalizace cest** - před porovnáváním jsou cesty normalizovány:
   - Odstranění query parametrů a fragmentů
   - Sjednocení oddělovačů adresářů (vždy `/`)
   - Odstranění opakujících se `/`

3. **Fuzzy matching** - pokud není nalezena přesná shoda, použije se fuzzy matching:
   - Porovnání podobnosti názvů souborů
   - Práce s obrázky s podobnými názvy (různé přípony, velikosti)

4. **Komplexní nahrazení** - nahrazení cest ve všech kontextech:
   - V atributech src, data-src
   - V CSS stylech
   - S ohledem na různé typy uvozovek

## 📋 Použití

### Samostatné spuštění

```bash
# Opravit všechny články
./fix_content_image_paths.sh --all

# Opravit konkrétní článek podle ID
./fix_content_image_paths.sh --id 1234

# Testovací běh bez ukládání změn
./fix_content_image_paths.sh --all --dry-run

# Opravit prvních 100 článků s podrobným výpisem
./fix_content_image_paths.sh --all --limit 100 --verbose
```

### Parametry

```
Použití: ./fix_content_image_paths.sh [MOŽNOSTI]

Možnosti:
  -h, --help          Zobrazí tuto nápovědu
  -a, --all           Opraví všechny články
  -i, --id ID         Opraví pouze článek s daným WordPress ID
  -l, --limit POČET   Maximální počet článků ke zpracování
  -s, --start POZICE  Index, od kterého začít zpracování
  -d, --dry-run       Pouze simulace bez skutečných změn
  -v, --verbose       Podrobnější výpis průběhu
```

### Integrace s kompletní migrací

Oprava cest k obrázkům je nyní zahrnuta jako další krok v procesu kompletní migrace:

```bash
# Kompletní migrace včetně rozšířené opravy cest k obrázkům
./run_complete_migration.sh

# Pouze opravy bez základní migrace
./run_complete_migration.sh --only-fixes
```

## 📊 Jak to funguje

1. **Analýza obsahu** - skript prochází HTML obsah článku a identifikuje všechny cesty k obrázkům
2. **Mapování** - pro každou nalezenou cestu:
   - Hledá odpovídající WordPress obrázek v mapování `image_map.json`
   - Pokud nenajde, zkouší najít podle názvu v databázi WordPress
   - Pokud stále nenajde, použije fuzzy matching pro nalezení podobného obrázku
3. **Nahrazení** - všechny nalezené cesty jsou nahrazeny správnými WordPress URL
4. **Aktualizace** - aktualizovaný obsah je uložen zpět do databáze WordPress

## 📝 Logování

Skript vytváří podrobné logy v adresáři `logs/`:

```
logs/fix_content_image_paths_YYYYMMDD_HHMMSS.log
```

Logy obsahují:
- Detailní informace o zpracovaných článcích
- Statistiky úspěšně opravených a přeskočených cest
- Případné chyby a varování
- Celkovou dobu trvání

## 🔧 Řešení problémů

1. **Nedostatek mapování** - pokud chybí mapování `image_map.json`, skript bude méně efektivní, ale stále bude fungovat s přímým vyhledáváním v databázi WordPress.

2. **Některé cesty nebyly opraveny** - pro složitější případy může být potřeba manuální oprava:
   - Zkontrolujte log soubor pro identifikaci problematických cest
   - Zvažte použití parametru `--verbose` pro podrobnější výpis

3. **Chyby při zpracování článků** - v případě chyby skript pokračuje na další článek, všechny chyby jsou zaznamenány v logu.
