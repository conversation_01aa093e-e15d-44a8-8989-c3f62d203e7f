
============================================================
KOMPLETNÍ MIGRACE A OPRAVA VŠECH OBRÁZKŮ
============================================================

Tento skript provede následující operace:
1. <PERSON><PERSON><PERSON><PERSON><PERSON> migraci (kategorie, uživatelé, obrázky, články, galerie)
2. Systematickou opravu všech galerií (preferování větších obrázků)
3. Opravu všech obrázků vložených v obsahu článků
4. Rozšířenou opravu cest k obrázkům s fuzzy matchingem

Chcete pokračovat? (y/n): 2025-06-03 10:38:04,241 - INFO - ========================================================
2025-06-03 10:38:04,241 - INFO - === ZAČÁTEK KOMPLETNÍ MIGRACE A OPRAVY VŠECH OBRÁZKŮ ===
2025-06-03 10:38:04,241 - INFO - ========================================================
2025-06-03 10:38:04,241 - INFO - Vytvářím nové virtuální prostředí ve složce '.venv'...
2025-06-03 10:38:06,821 - INFO - Virtuální prostředí bylo úspěšně vytvořeno.
2025-06-03 10:38:06,833 - INFO - Instaluji potřebné závislosti ve virtuálním prostředí...
Collecting psycopg2-binary
  Using cached psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)
Collecting mysql-connector-python
  Downloading mysql_connector_python-9.3.0-cp312-cp312-manylinux_2_28_x86_64.whl.metadata (7.5 kB)
Collecting python-dotenv
  Using cached python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)
Collecting Pillow
  Downloading pillow-11.2.1-cp312-cp312-manylinux_2_28_x86_64.whl.metadata (8.9 kB)
Collecting python-slugify
  Using cached python_slugify-8.0.4-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting beautifulsoup4
  Downloading beautifulsoup4-4.13.4-py3-none-any.whl.metadata (3.8 kB)
Collecting text-unidecode>=1.3 (from python-slugify)
  Using cached text_unidecode-1.3-py2.py3-none-any.whl.metadata (2.4 kB)
Collecting soupsieve>1.2 (from beautifulsoup4)
  Downloading soupsieve-2.7-py3-none-any.whl.metadata (4.6 kB)
Collecting typing-extensions>=4.0.0 (from beautifulsoup4)
  Downloading typing_extensions-4.14.0-py3-none-any.whl.metadata (3.0 kB)
Using cached psycopg2_binary-2.9.10-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)
Downloading mysql_connector_python-9.3.0-cp312-cp312-manylinux_2_28_x86_64.whl (33.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 33.9/33.9 MB 4.6 MB/s eta 0:00:00
Using cached python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Downloading pillow-11.2.1-cp312-cp312-manylinux_2_28_x86_64.whl (4.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.6/4.6 MB 2.5 MB/s eta 0:00:00
Using cached python_slugify-8.0.4-py2.py3-none-any.whl (10 kB)
Downloading beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 187.3/187.3 kB 1.8 MB/s eta 0:00:00
Downloading soupsieve-2.7-py3-none-any.whl (36 kB)
Using cached text_unidecode-1.3-py2.py3-none-any.whl (78 kB)
Downloading typing_extensions-4.14.0-py3-none-any.whl (43 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.8/43.8 kB 2.3 MB/s eta 0:00:00
Installing collected packages: text-unidecode, typing-extensions, soupsieve, python-slugify, python-dotenv, psycopg2-binary, Pillow, mysql-connector-python, beautifulsoup4
Successfully installed Pillow-11.2.1 beautifulsoup4-4.13.4 mysql-connector-python-9.3.0 psycopg2-binary-2.9.10 python-dotenv-1.1.0 python-slugify-8.0.4 soupsieve-2.7 text-unidecode-1.3 typing-extensions-4.14.0
2025-06-03 10:38:19,737 - INFO - Závislosti byly úspěšně nainstalovány ve virtuálním prostředí.
2025-06-03 10:38:19,738 - INFO - === SPOUŠTÍM ZÁKLADNÍ MIGRACI ===
2025-06-03 10:38:19,781 - INFO - === ZAČÁTEK MIGRACE ===
2025-06-03 10:38:19,831 - WARNING - Chybí některé závislosti: No module named 'dotenv'
2025-06-03 10:38:19,831 - INFO - Instaluji chybějící závislosti...
error: externally-managed-environment

× This environment is externally managed
╰─> To install Python packages system-wide, try apt install
    python3-xyz, where xyz is the package you are trying to
    install.
    
    If you wish to install a non-Debian-packaged Python package,
    create a virtual environment using python3 -m venv path/to/venv.
    Then use path/to/venv/bin/python and path/to/venv/bin/pip. Make
    sure you have python3-full installed.
    
    If you wish to install a non-Debian packaged Python application,
    it may be easiest to use pipx install xyz, which will manage a
    virtual environment for you. Make sure you have pipx installed.
    
    See /usr/share/doc/python3.12/README.venv for more information.

note: If you believe this is a mistake, please contact your Python installation or OS distribution provider. You can override this, at the risk of breaking your Python installation or OS, by passing --break-system-packages.
hint: See PEP 668 for the detailed specification.
2025-06-03 10:38:20,251 - ERROR - Chyba při instalaci závislostí: Command '['/usr/bin/python3', '-m', 'pip', 'install', 'psycopg2-binary', 'mysql-connector-python', 'python-dotenv', 'Pillow', 'python-slugify', 'beautifulsoup4']' returned non-zero exit status 1.
2025-06-03 10:38:20,251 - ERROR - Nelze pokračovat bez potřebných závislostí.

Migrace selhala. Pro detaily zkontrolujte log soubor.
2025-06-03 10:38:20,269 - INFO - Základní migrace dokončena úspěšně.
2025-06-03 10:38:20,269 - INFO - === SPOUŠTÍM OPRAVU VŠECH GALERIÍ ===
2025-06-03 10:38:20,345 - INFO - Spouštím systematickou opravu všech galerií (dry_run=False, limit=None, start_from=0)...
Traceback (most recent call last):
  File "/home/<USER>/sabre/sabre-migrace/fix_all_galleries_systematic.py", line 240, in <module>
    fix_all_galleries_systematic(args.dry_run, args.limit, args.start_from, args.verbose)
  File "/home/<USER>/sabre/sabre-migrace/fix_all_galleries_systematic.py", line 41, in fix_all_galleries_systematic
    mysql_conn = get_mysql_connection()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/sabre/sabre-migrace/fix_gallery_mapping_add_images.py", line 90, in get_mysql_connection
    return mysql.connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/mysql/connector/__init__.py", line 173, in connect
    return MySQLConnection(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/mysql/connector/connection.py", line 102, in __init__
    self.connect(**kwargs)
  File "/usr/lib/python3/dist-packages/mysql/connector/abstracts.py", line 735, in connect
    self._open_connection()
  File "/usr/lib/python3/dist-packages/mysql/connector/connection.py", line 250, in _open_connection
    self._do_auth(self._user, self._password,
  File "/usr/lib/python3/dist-packages/mysql/connector/connection.py", line 172, in _do_auth
    self._auth_switch_request(username, password)
  File "/usr/lib/python3/dist-packages/mysql/connector/connection.py", line 216, in _auth_switch_request
    raise errors.get_exception(packet)
mysql.connector.errors.ProgrammingError: 1049 (42000): Unknown database 'mujdum-2'
2025-06-03 10:38:20,450 - ERROR - Chyba při opravě galerií: Command '['/usr/bin/python3', 'fix_all_galleries_systematic.py', '--verbose']' returned non-zero exit status 1.
2025-06-03 10:38:20,451 - ERROR - Oprava galerií selhala.
Chcete pokračovat s opravou obrázků v obsahu? (y/n): 
Migrace nebo oprava selhaly.
Pro detaily zkontrolujte log soubor: logs/complete_migration_20250603_103756.log
