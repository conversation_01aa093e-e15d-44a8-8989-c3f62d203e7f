#!/usr/bin/env python3
import os
import sys
import time
import logging
import argparse
import subprocess
from datetime import datetime

# Nastavení logování
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"complete_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Nastavení formátu logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

def setup_virtualenv():
    """Nastaví virtuální prostředí pro migraci."""
    venv_dir = ".venv"
    activate_script = os.path.join(venv_dir, "bin", "activate")
    
    # Kontrola, zda virtuální prostředí již existuje
    if os.path.exists(venv_dir) and os.path.isfile(activate_script):
        logging.info(f"Virtuální prostředí již existuje ve složce '{venv_dir}'.")
        return venv_dir
    
    # Vytvoření nového virtuálního prostředí
    logging.info(f"Vytvářím nové virtuální prostředí ve složce '{venv_dir}'...")
    try:
        subprocess.run([sys.executable, "-m", "venv", venv_dir], check=True)
        logging.info("Virtuální prostředí bylo úspěšně vytvořeno.")
        return venv_dir
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při vytváření virtuálního prostředí: {e}")
        return None

def check_dependencies():
    """Kontrola závislostí ve virtuálním prostředí."""
    venv_dir = setup_virtualenv()
    if not venv_dir:
        logging.error("Nelze zkontrolovat závislosti bez virtuálního prostředí.")
        return False
    
    # Určení cesty k python a pip z virtuálního prostředí
    venv_python = os.path.join(venv_dir, "bin", "python")
    venv_pip = os.path.join(venv_dir, "bin", "pip")
    
    # Kontrola potřebných balíčků
    try:
        # Zkusíme importovat balíčky pomocí venv Python
        result = subprocess.run(
            [venv_python, "-c", "import psycopg2, mysql.connector, dotenv, bs4, slugify, PIL"],
            stderr=subprocess.PIPE,
            text=True
        )
        
        if result.returncode == 0:
            logging.info("Všechny závislosti jsou již nainstalovány ve virtuálním prostředí.")
            return True
        
        # Instalace chybějících závislostí
        logging.info("Instaluji potřebné závislosti ve virtuálním prostředí...")
        subprocess.run([
            venv_pip, "install",
            "psycopg2-binary", "mysql-connector-python", "python-dotenv",
            "Pillow", "python-slugify", "beautifulsoup4"
        ], check=True)
        
        logging.info("Závislosti byly úspěšně nainstalovány ve virtuálním prostředí.")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při kontrole nebo instalaci závislostí: {e}")
        if e.stderr:
            logging.error(f"Detaily chyby: {e.stderr}")
        return False

def run_basic_migration(reset=False):
    """Spustí základní migrační proces (kategorie, uživatelé, obrázky, články, galerie)."""
    logging.info("=== SPOUŠTÍM ZÁKLADNÍ MIGRACI ===")
    
    cmd = [sys.executable, "run_migration.py"]
    if reset:
        cmd.append("--reset")
    
    try:
        subprocess.run(cmd, check=True)
        logging.info("Základní migrace dokončena úspěšně.")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při základní migraci: {e}")
        return False

def fix_all_galleries():
    """Spustí systematickou opravu všech galerií."""
    logging.info("=== SPOUŠTÍM OPRAVU VŠECH GALERIÍ ===")
    
    try:
        subprocess.run([sys.executable, "fix_all_galleries_systematic.py", "--verbose"], check=True)
        logging.info("Oprava galerií dokončena úspěšně.")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při opravě galerií: {e}")
        return False

def fix_content_images():
    """Spustí opravu obrázků vložených v obsahu článků."""
    logging.info("=== SPOUŠTÍM OPRAVU OBRÁZKŮ V OBSAHU ===")
    
    try:
        subprocess.run([sys.executable, "fix_post_content_images.py", "--all", "--verbose"], check=True)
        logging.info("Oprava obrázků v obsahu dokončena úspěšně.")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při opravě obrázků v obsahu: {e}")
        return False

def fix_image_paths():
    """Spustí rozšířenou opravu cest k obrázkům v obsahu článků."""
    logging.info("=== SPOUŠTÍM ROZŠÍŘENOU OPRAVU CEST K OBRÁZKŮM V OBSAHU ===")
    
    try:
        subprocess.run([sys.executable, "fix_content_image_paths.py", "--all", "--verbose"], check=True)
        logging.info("Rozšířená oprava cest k obrázkům v obsahu dokončena úspěšně.")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při rozšířené opravě cest k obrázkům v obsahu: {e}")
        return False

def run_dry_test():
    """Spustí testovací běh migrace a oprav bez skutečného provedení změn."""
    logging.info("=== PROVÁDÍM TESTOVACÍ BĚH (DRY-RUN) ===")
    
    # Test galerií s limitem 3 článků
    logging.info("Test opravy galerií (3 články)...")
    try:
        subprocess.run([sys.executable, "fix_all_galleries_systematic.py", "--dry-run", "--limit", "3", "--verbose"], check=True)
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při testu opravy galerií: {e}")
    
    # Test opravy obrázků v obsahu
    logging.info("Test opravy obrázků v obsahu (3 články)...")
    try:
        subprocess.run([sys.executable, "fix_post_content_images.py", "--all", "--dry-run", "--limit", "3", "--verbose"], check=True)
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při testu opravy obrázků v obsahu: {e}")
    
    # Test rozšířené opravy cest k obrázkům
    logging.info("Test rozšířené opravy cest k obrázkům (3 články)...")
    try:
        subprocess.run([sys.executable, "fix_content_image_paths.py", "--all", "--dry-run", "--limit", "3", "--verbose"], check=True)
    except subprocess.CalledProcessError as e:
        logging.error(f"Chyba při testu rozšířené opravy cest k obrázkům: {e}")
    
    logging.info("Testovací běh dokončen.")
    return True

def complete_migration(reset=False, only_fixes=False, only_images=False, dry_run=False):
    """Provede kompletní migraci včetně všech oprav."""
    start_time = time.time()
    
    logging.info("========================================================")
    logging.info("=== ZAČÁTEK KOMPLETNÍ MIGRACE A OPRAVY VŠECH OBRÁZKŮ ===")
    logging.info("========================================================")
    
    # 1. Kontrola závislostí
    if not check_dependencies():
        logging.error("Nelze pokračovat bez potřebných závislostí.")
        return False
    
    # 2. Testovací běh, pokud je požadován
    if dry_run:
        run_dry_test()
        logging.info("Testovací běh dokončen, ukončuji.")
        return True
    
    # 3. Základní migrace (kategorie, uživatelé, obrázky, články, galerie)
    if not only_fixes:
        if not run_basic_migration(reset=reset):
            logging.error("Základní migrace selhala.")
            if input("Chcete pokračovat s opravami i přesto? (y/n): ").lower() != 'y':
                return False
    else:
        logging.info("Přeskakuji základní migraci, provádím pouze opravy.")
    
    # 4. Oprava všech galerií
    if not fix_all_galleries():
        logging.error("Oprava galerií selhala.")
        if input("Chcete pokračovat s opravou obrázků v obsahu? (y/n): ").lower() != 'y':
            return False
    
    # 5. Oprava obrázků v obsahu
    if not fix_content_images():
        logging.error("Oprava obrázků v obsahu selhala.")
        if input("Chcete pokračovat s rozšířenou opravou cest k obrázkům? (y/n): ").lower() != 'y':
            return False
    
    # 6. Rozšířená oprava cest k obrázkům v obsahu
    if not fix_image_paths():
        logging.error("Rozšířená oprava cest k obrázkům selhala.")
    
    # 7. Shrnutí
    end_time = time.time()
    duration = end_time - start_time
    hours, remainder = divmod(duration, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    logging.info("========================================================")
    logging.info("=== KONEC KOMPLETNÍ MIGRACE A OPRAVY VŠECH OBRÁZKŮ ===")
    logging.info("========================================================")
    logging.info(f"Celková doba trvání: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    logging.info(f"Log soubor: {log_file}")
    
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Kompletní migrační a opravný skript pro přenos dat z PostgreSQL do WordPress')
    parser.add_argument('--reset', action='store_true', help='Resetovat WordPress databázi před migrací')
    parser.add_argument('--only-fixes', action='store_true', help='Přeskočit základní migraci a provést pouze opravy')
    parser.add_argument('--only-images', action='store_true', help='Spustit pouze migraci obrázků')
    parser.add_argument('--dry-run', action='store_true', help='Provést testovací běh bez ukládání změn')
    args = parser.parse_args()
    
    print("\n" + "="*60)
    print("KOMPLETNÍ MIGRACE A OPRAVA VŠECH OBRÁZKŮ")
    print("="*60)
    
    if not args.dry_run:
        print("\nTento skript provede následující operace:")
        print("1. Základní migraci (kategorie, uživatelé, obrázky, články, galerie)")
        print("2. Systematickou opravu všech galerií (preferování větších obrázků)")
        print("3. Opravu všech obrázků vložených v obsahu článků")
        print("4. Rozšířenou opravu cest k obrázkům s fuzzy matchingem")
        
        if args.reset:
            print("\nVARNING: Bude provedeno resetování WordPress databáze!")
        
        if args.only_fixes:
            print("\nBude provedena pouze oprava, základní migrace bude přeskočena.")
        
        if args.only_images:
            print("\nBude provedena pouze migrace a oprava obrázků.")
        
        consent = input("\nChcete pokračovat? (y/n): ")
        if consent.lower() != 'y':
            print("Operace zrušena.")
            sys.exit(0)
    
    success = complete_migration(
        reset=args.reset,
        only_fixes=args.only_fixes,
        only_images=args.only_images,
        dry_run=args.dry_run
    )
    
    if success:
        print("\nKompletní migrace a oprava úspěšně dokončeny.")
        print(f"Pro detaily zkontrolujte log soubor: {log_file}")
    else:
        print("\nMigrace nebo oprava selhaly.")
        print(f"Pro detaily zkontrolujte log soubor: {log_file}")
