# Complete Migration Guide

This guide covers the comprehensive reset and migration system for migrating content from PostgreSQL to WordPress.

## Overview

The migration system consists of several components:

1. **complete_reset_migration.py** - Main script for complete reset and migration
2. **test_migration_steps.py** - Testing and validation utilities
3. Individual migration scripts (migrate_*.py, fix_*.py)

## Quick Start

### Full Reset and Migration
```bash
# Complete reset and migration (DESTRUCTIVE!)
python complete_reset_migration.py

# Skip cleanup, run migration only
python complete_reset_migration.py --skip-cleanup

# Start from specific step
python complete_reset_migration.py --start-from=images

# Auto-confirm all prompts (DANGEROUS!)
python complete_reset_migration.py --yes
```

### Testing and Validation
```bash
# Check current migration status
python test_migration_steps.py --check-status

# Validate image migration
python test_migration_steps.py --validate-images

# Check for duplicates
python test_migration_steps.py --check-duplicates

# Test database connections
python test_migration_steps.py --test-connections

# Run all tests
python test_migration_steps.py --all
```

## Migration Steps

The complete migration pipeline consists of 8 steps:

### 1. Cleanup
- **Purpose**: Reset WordPress database and files to clean state
- **Actions**:
  - Delete all migrated posts, users, categories, media
  - Remove mapping files (image_map.json, article_map.json, etc.)
  - Delete uploaded WordPress media files
  - Reset auto-increment counters
- **Safety**: Requires user confirmation
- **Skip**: Use `--skip-cleanup` flag

### 2. Categories
- **Script**: `migrate_categories.py`
- **Purpose**: Migrate categories/rubrics from PostgreSQL
- **Output**: Creates `category_map.json` mapping file

### 3. Users
- **Script**: `migrate_users.py`
- **Purpose**: Migrate users from PostgreSQL
- **Output**: Creates `user_map.json` mapping file

### 4. Images
- **Script**: `migrate_images.py`
- **Purpose**: Migrate images with fixed subdirectory logic
- **Features**:
  - Finds images in subdirectories based on polozka_id
  - Handles both obrazek table and galerie_polozka table
  - Creates WordPress media attachments
- **Output**: Creates `image_map.json` mapping file

### 5. Articles
- **Script**: `migrate_articles.py`
- **Purpose**: Migrate articles with proper featured image assignment
- **Features**:
  - Migrates article content and metadata
  - Assigns featured images using advanced matching
  - Links articles to categories and users
- **Output**: Creates `article_map.json` mapping file

### 6. Fix Content
- **Script**: `fix_remaining_images.py`
- **Purpose**: Replace old image URLs in article content
- **Features**:
  - Uses advanced regex to find old URLs (including those with spaces)
  - Intelligent image matching with normalization
  - Handles both `/obrazek/` and `obrazek/` patterns

### 7. Fix Featured
- **Script**: `fix_article_images.py`
- **Purpose**: Optimize featured images to reduce duplicates
- **Features**:
  - Finds better featured images for articles using fallback images
  - Advanced image matching algorithm
  - Reduces duplicate featured images

### 8. Validation
- **Purpose**: Final validation and reporting
- **Checks**:
  - Count migrated content
  - Verify no old image URLs remain
  - Check featured image assignments
  - Generate comprehensive report

## Safety Features

### User Confirmation Prompts
- Database cleanup requires explicit confirmation
- File deletion requires separate confirmation
- Failed steps prompt whether to continue
- Final warning before destructive operations

### Error Handling
- Each step has comprehensive error handling
- Failed steps don't abort entire migration (with user choice)
- Detailed logging to both file and console
- Resume capability from any step

### Data Protection
- PostgreSQL source database remains untouched
- All operations are on WordPress/MySQL target only
- Mapping files preserve migration history
- Detailed logs for troubleshooting

## Resume and Recovery

### Resume from Specific Step
```bash
# Resume from images step
python complete_reset_migration.py --start-from=images

# Resume from validation only
python complete_reset_migration.py --start-from=validation
```

### Manual Step Execution
```bash
# Run individual migration scripts
python migrate_categories.py
python migrate_users.py
python migrate_images.py
python migrate_articles.py
python fix_remaining_images.py
python fix_article_images.py
```

## Monitoring and Reporting

### Real-time Monitoring
- Detailed console output with progress indicators
- Step-by-step timing information
- Success/failure status for each operation
- Error messages with context

### Migration Report
The system generates a comprehensive JSON report containing:

```json
{
  "migration_date": "2025-06-03T14:30:00",
  "total_duration_seconds": 1800,
  "total_duration_formatted": "30.0 minutes",
  "steps_completed": ["cleanup", "categories", "users", "images", "articles"],
  "step_durations": {
    "cleanup": 45.2,
    "categories": 12.5,
    "users": 8.3,
    "images": 1200.7,
    "articles": 450.1
  },
  "migration_statistics": {
    "categories": {"processed": 156, "failed": 0, "success_rate": 100.0},
    "users": {"processed": 89, "failed": 1, "success_rate": 98.9},
    "images": {"processed": 40863, "failed": 25, "success_rate": 99.94},
    "articles": {"processed": 6399, "failed": 0, "success_rate": 100.0}
  },
  "validation_results": {
    "articles": 6399,
    "images": 40863,
    "users": 88,
    "categories": 156,
    "featured_images": 5234,
    "old_image_urls": 0
  },
  "errors": [],
  "success": true
}
```

### Log Files
- Timestamped log files: `migration_YYYYMMDD_HHMMSS.log`
- Detailed operation logs with timestamps
- Error traces for debugging
- Performance metrics

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Test connections first
python test_migration_steps.py --test-connections
```

#### Missing Scripts
- Ensure all migration scripts are in the same directory
- Check file permissions
- Verify Python dependencies

#### Memory Issues
- Large image migrations may require increased memory
- Monitor system resources during migration
- Consider running in smaller batches

#### Partial Failures
- Use `--start-from` to resume from failed step
- Check logs for specific error details
- Validate prerequisites before retrying

### Recovery Procedures

#### Complete Reset
```bash
# Full reset and restart
python complete_reset_migration.py
```

#### Partial Reset
```bash
# Reset only database, keep files
# (Manual database cleanup required)

# Reset only files, keep database
# (Manual file cleanup required)
```

#### Validation Only
```bash
# Check current state without changes
python test_migration_steps.py --all
python complete_reset_migration.py --start-from=validation
```

## Performance Optimization

### Database Optimization
- Ensure proper indexing on source tables
- Use connection pooling for large migrations
- Monitor database performance during migration

### File System Optimization
- Ensure sufficient disk space for media files
- Use SSD storage for better I/O performance
- Monitor disk usage during image migration

### Memory Management
- Close database connections properly
- Process large datasets in batches
- Monitor memory usage during migration

## Best Practices

1. **Always test first**: Use test environment before production
2. **Backup everything**: Backup both source and target databases
3. **Monitor progress**: Watch logs and system resources
4. **Validate results**: Run validation tests after migration
5. **Document changes**: Keep migration reports for reference
6. **Plan downtime**: Schedule migration during low-traffic periods
7. **Test rollback**: Have rollback procedures ready

## Support and Maintenance

### Regular Checks
```bash
# Weekly validation
python test_migration_steps.py --all

# Check for issues
python test_migration_steps.py --check-duplicates
```

### Updates and Patches
- Keep migration scripts updated
- Test script changes in development environment
- Document any customizations made

### Monitoring
- Set up alerts for migration failures
- Monitor system resources during large migrations
- Keep logs for historical analysis
