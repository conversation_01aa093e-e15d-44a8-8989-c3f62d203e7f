#!/usr/bin/env python3
import logging
import os
import json
import sys
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_OBRAZEK,
    TBL_WP_POSTS, TBL_WP_POSTMETA
)
from utils import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def diagnose_gallery_migration(article_id=None):
    """
    Diagnose why only one image is being migrated for galleries
    """
    logging.info("Spouštím diagnostiku migrace galerií...")
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    # Načíst mapování
    try:
        image_map = load_mapping('image_map.json')
        logging.info(f"Načteno {len(image_map)} záznamů z image_map.json")
    except Exception as e:
        logging.error(f"Chyba při načítání image_map.json: {e}")
        image_map = {}
    
    try:
        # Získat konkrétní článek pro analýzu
        if article_id:
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, nazev 
                FROM {TBL_CLANEK} 
                WHERE id_clanek = %s
            """, (article_id,))
        else:
            # Nebo první článek s unikatním ID
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, nazev 
                FROM {TBL_CLANEK} 
                WHERE unikatni_id IS NOT NULL
                ORDER BY id_clanek DESC
                LIMIT 1
            """)
        
        article_info = pg_cursor.fetchone()
        
        if not article_info:
            logging.error("Nenalezen žádný článek pro diagnostiku")
            return
        
        article_id, unique_id, article_title = article_info
        logging.info(f"Diagnostika pro článek ID {article_id}: {article_title}")
        
        # Získat obrázky článku z PostgreSQL
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, typ, priorita, active_state 
            FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s
            ORDER BY typ, priorita DESC
        """, (unique_id,))
        
        obrazky = pg_cursor.fetchall()
        logging.info(f"Nalezeno {len(obrazky)} obrázků v PostgreSQL")
        
        # Vypsat detaily obrázků
        for i, (img_id, img_file, img_type, img_prio, img_active) in enumerate(obrazky, 1):
            logging.info(f"Obrázek {i}: ID={img_id}, Soubor={img_file}, Typ={img_type}, Priorita={img_prio}, Aktivní={img_active}")
            
            # Kontrola, zda je obrázek v mapování
            if img_file in image_map:
                wp_img_id = image_map[img_file].get('wp_id')
                logging.info(f"  - Obrázek je v mapování, WordPress ID: {wp_img_id}")
            else:
                logging.info(f"  - Obrázek NENÍ v mapování")
                
                # Zkusit najít podle základního názvu
                base_name, ext = os.path.splitext(img_file)
                found = False
                
                for map_key in image_map:
                    if map_key.startswith(base_name) or os.path.basename(map_key).startswith(base_name):
                        wp_img_id = image_map[map_key].get('wp_id')
                        logging.info(f"  - Nalezen podobný obrázek v mapování podle základního názvu: {map_key}, WordPress ID: {wp_img_id}")
                        found = True
                        break
                
                if not found:
                    logging.warning(f"  - Obrázek není nalezen v mapování ani podle základního názvu")
        
        # Analýza WordPress dat
        mysql_cursor.execute(f"""
            SELECT wp.ID, wp.post_title
            FROM {TBL_WP_POSTS} wp
            JOIN {TBL_WP_POSTMETA} wpm ON wp.ID = wpm.post_id
            WHERE wpm.meta_key = 'sabre_original_id' AND wpm.meta_value = %s
        """, (article_id,))
        
        wp_article = mysql_cursor.fetchone()
        
        if not wp_article:
            logging.error(f"Článek ID {article_id} nebyl nalezen ve WordPress")
            return
        
        wp_article_id, wp_article_title = wp_article
        logging.info(f"WordPress článek: ID={wp_article_id}, Titulek={wp_article_title}")
        
        # Získat metadata galerie
        mysql_cursor.execute(f"""
            SELECT meta_value
            FROM {TBL_WP_POSTMETA}
            WHERE post_id = %s AND meta_key = 'sabre_gallery'
        """, (wp_article_id,))
        
        gallery_meta = mysql_cursor.fetchone()
        
        if not gallery_meta:
            logging.warning(f"Článek nemá žádná metadata galerie")
            return
        
        gallery_data = json.loads(gallery_meta[0])
        logging.info(f"Metadata galerie: {json.dumps(gallery_data, indent=2, ensure_ascii=False)}")
        
        # Analýza obsahu článku
        mysql_cursor.execute(f"""
            SELECT post_content
            FROM {TBL_WP_POSTS}
            WHERE ID = %s
        """, (wp_article_id,))
        
        content = mysql_cursor.fetchone()[0]
        gallery_shortcode_index = content.find("[gallery")
        
        if gallery_shortcode_index >= 0:
            shortcode_end = content.find("]", gallery_shortcode_index)
            gallery_shortcode = content[gallery_shortcode_index:shortcode_end+1]
            logging.info(f"Nalezen shortcode v článku: {gallery_shortcode}")
        else:
            logging.warning("Shortcode galerie nebyl nalezen v obsahu článku")
        
    except Exception as e:
        logging.error(f"Chyba při diagnostice: {e}")
        import traceback
        logging.error(traceback.format_exc())
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

if __name__ == "__main__":
    article_id = None
    if len(sys.argv) > 1:
        try:
            article_id = int(sys.argv[1])
        except ValueError:
            logging.error(f"Neplatné ID článku: {sys.argv[1]}")
            sys.exit(1)
    
    diagnose_gallery_migration(article_id)
