#!/usr/bin/env python3
"""
Skript pro přiřazení featured images k článkům.
Najde vhodný obrázek pro každý článek a nastaví ho jako featured image.
"""

import logging
import json
import re
from datetime import datetime
from db_connectors import get_pg_connection, get_mysql_connection
from config import TBL_CLANEK, TBL_WP_POSTS, TBL_WP_POSTMETA
from utils import load_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_date_from_slug(slug):
    """
    Extrahuje datum z názvu obrázku (slug).
    Hledá hexadecimální timestamp na začátku názvu.
    """
    # Hledat hexadecimální číslo na začátku (timestamp)
    match = re.match(r'^([0-9a-f]+)', slug.lower())
    if match:
        try:
            # Přev<PERSON>t hex na timestamp
            hex_timestamp = match.group(1)
            if len(hex_timestamp) >= 8:  # Minimálně 8 znaků pro rozumný timestamp
                timestamp = int(hex_timestamp[:8], 16)
                # Zkontrolovat, jestli je timestamp rozumný (po roce 2000)
                if timestamp > 946684800:  # 1.1.2000
                    return datetime.fromtimestamp(timestamp)
        except (ValueError, OSError):
            pass
    return None

def find_best_image_for_article(article_id, article_date, mysql_cursor):
    """
    Najde nejlepší obrázek pro článek na základě časového razítka.
    
    Args:
        article_id (int): ID článku v WordPress
        article_date (datetime): Datum vytvoření článku
        mysql_cursor: MySQL cursor
    
    Returns:
        int: ID obrázku nebo None
    """
    # Získat všechny dostupné obrázky
    mysql_cursor.execute(f"""
        SELECT ID, post_name, post_date
        FROM {TBL_WP_POSTS}
        WHERE post_type = 'attachment'
        AND post_mime_type LIKE 'image/%'
        AND post_parent = 0
        ORDER BY post_date
    """)
    
    images = mysql_cursor.fetchall()
    
    if not images:
        return None
    
    best_image = None
    best_score = float('inf')
    
    for img_id, img_slug, img_date in images:
        score = 0
        
        # 1. Pokusit se extrahovat datum z názvu obrázku
        extracted_date = extract_date_from_slug(img_slug)
        if extracted_date:
            # Spočítat rozdíl v dnech
            time_diff = abs((extracted_date - article_date).days)
            score = time_diff
        else:
            # Pokud nelze extrahovat datum, použít datum uploadu
            time_diff = abs((img_date - article_date).days)
            score = time_diff + 1000  # Penalizace za neextrahované datum
        
        # 2. Bonus pro obrázky s určitými klíčovými slovy
        if any(keyword in img_slug.lower() for keyword in ['kontakty', 'uvod', 'main', 'featured']):
            score -= 50
        
        # 3. Penalizace pro velmi malé nebo velmi velké obrázky (podle ID)
        if img_id < 1000 or img_id > 50000:
            score += 100
        
        if score < best_score:
            best_score = score
            best_image = img_id
    
    return best_image

def assign_featured_images():
    """Přiřadí featured images k článkům, které je nemají."""
    logging.info("Spouštím přiřazování featured images...")
    
    # Načíst mapování článků
    article_map = load_mapping('article_map.json')
    logging.info(f"Načteno {len(article_map)} článků z mapování.")
    
    # Připojení k databázím
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Získat články bez featured image
        mysql_cursor.execute(f"""
            SELECT p.ID, p.post_title, p.post_date
            FROM {TBL_WP_POSTS} p
            LEFT JOIN {TBL_WP_POSTMETA} pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
            WHERE p.post_type = 'post'
            AND p.post_status = 'publish'
            AND (pm.meta_value IS NULL OR pm.meta_value = '')
            ORDER BY p.post_date
        """)
        
        articles_without_featured = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(articles_without_featured)} článků bez featured image.")
        
        processed = 0
        assigned = 0
        not_found = 0
        
        for wp_post_id, post_title, post_date in articles_without_featured:
            # Najít nejlepší obrázek pro článek
            best_image_id = find_best_image_for_article(wp_post_id, post_date, mysql_cursor)
            
            if best_image_id:
                # Nastavit featured image
                mysql_cursor.execute(f"""
                    INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value)
                    VALUES (%s, '_thumbnail_id', %s)
                    ON DUPLICATE KEY UPDATE meta_value = %s
                """, (wp_post_id, best_image_id, best_image_id))
                
                # Přiřadit obrázek k článku (nastavit post_parent)
                mysql_cursor.execute(f"""
                    UPDATE {TBL_WP_POSTS}
                    SET post_parent = %s
                    WHERE ID = %s AND post_parent = 0
                """, (wp_post_id, best_image_id))
                
                assigned += 1
                logging.info(f"✓ Článek '{post_title[:50]}...' (ID: {wp_post_id}) - přiřazen obrázek ID {best_image_id}")
            else:
                not_found += 1
                logging.warning(f"✗ Článek '{post_title[:50]}...' (ID: {wp_post_id}) - žádný vhodný obrázek nenalezen")
            
            processed += 1
            
            if processed % 100 == 0:
                logging.info(f"Zpracováno {processed}/{len(articles_without_featured)} článků...")
                mysql_conn.commit()
        
        # Finální commit
        mysql_conn.commit()
        
        logging.info("=" * 60)
        logging.info("SOUHRN PŘIŘAZOVÁNÍ FEATURED IMAGES:")
        logging.info(f"Celkem zpracováno: {processed}")
        logging.info(f"Úspěšně přiřazeno: {assigned}")
        logging.info(f"Nenalezen vhodný obrázek: {not_found}")
        logging.info("=" * 60)
        
    except Exception as e:
        logging.error(f"Chyba při přiřazování featured images: {e}")
        mysql_conn.rollback()
    finally:
        pg_cursor.close()
        pg_conn.close()
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    assign_featured_images()
