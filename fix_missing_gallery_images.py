#!/usr/bin/env python3
import logging
import os
import json
import sys
import mysql.connector
from datetime import datetime
import re

# Nastavení logování
LOG_FILE = os.path.join('logs', f'fix_gallery_images_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_mysql_connection():
    """
    Vytvoří a vrátí spojení s MySQL databází pro WordPress 
    na základě konfigurace v .env souboru
    """
    # Načtení konfigurace z .env souboru
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
    
    return wp_prefix

def fix_gallery_entries():
    """
    Opraví záznamy galerií, které mají pouze jeden obrázek, přestože by měly mít více.
    Projde všechny články s metadaty sabre_gallery a upraví je tak, aby obsahovaly všechny obrázky.
    """
    logging.info("Spouštím opravu galerií s chybějícími obrázky...")
    
    try:
        # Připojení k databázi
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Načtení mapování
        image_map = load_mapping('image_map.json')
        gallery_map = load_mapping('gallery_map.json')
        
        if not image_map:
            logging.error("Chybí mapování obrázků. Nejprve spusťte migraci obrázků.")
            return
        
        wp_prefix = get_table_prefixes()
        
        # Získat všechny záznamy s metadaty sabre_gallery
        mysql_cursor.execute(f"""
            SELECT post_id, meta_id, meta_value
            FROM {wp_prefix}postmeta
            WHERE meta_key = 'sabre_gallery'
        """)
        
        galleries = mysql_cursor.fetchall()
        logging.info(f"Nalezeno {len(galleries)} záznamů s metadaty sabre_gallery.")
        
        # Statistiky
        updated_count = 0
        failed_count = 0
        already_ok_count = 0
        
        # Pro každý záznam
        for gallery in galleries:
            try:
                post_id = gallery['post_id']
                meta_id = gallery['meta_id']
                meta_value = gallery['meta_value']
                
                # Dekódovat JSON metadata
                try:
                    gallery_data = json.loads(meta_value)
                except json.JSONDecodeError:
                    logging.error(f"Neplatný JSON v metadatech pro post_id {post_id}")
                    failed_count += 1
                    continue
                
                # Původní počet obrázků
                original_image_count = len(gallery_data.get('image_ids', []))
                
                # Pokud nemáme original_unique_id, nemůžeme opravit
                if 'original_unique_id' not in gallery_data:
                    logging.warning(f"Post ID {post_id}: Chybí original_unique_id, nelze opravit")
                    continue
                
                unique_id = gallery_data['original_unique_id']
                article_title = gallery_data.get('article_title', 'Bez názvu')
                
                # Zkontrolovat, zda jsou v DB dostupné data
                mysql_cursor.execute(f"""
                    SELECT post_content
                    FROM {wp_prefix}posts
                    WHERE ID = %s
                """, (post_id,))
                
                post_result = mysql_cursor.fetchone()
                if not post_result:
                    logging.warning(f"Post ID {post_id} neexistuje v databázi")
                    failed_count += 1
                    continue
                
                post_content = post_result['post_content']
                
                # Získat všechny obrázky s tímto unique_id z PostgreSQL
                # Tady použijeme přímý SQL dotaz, protože použití pg modulu by vyžadovalo instalaci
                # Získat všechny shortcodes gallery v obsahu příspěvku
                gallery_shortcodes = re.findall(r'\[gallery[^\]]*\]', post_content)
                
                logging.info(f"Post ID {post_id} ({article_title}): Nalezeno {len(gallery_shortcodes)} gallery shortcodes")
                
                # Pokud máme unique_id, můžeme doplnit galerii na základě načtených souborů
                # Zde bychom normálně načetli data z PostgreSQL, ale protože nemáme modul,
                # budeme hledat v image_map všechny obrázky, které obsahují unique_id v názvu
                
                new_image_ids = []
                
                # Procházet všechny obrázky v mapování a hledat ty, které patří k tomuto článku
                for image_file, image_data in image_map.items():
                    if unique_id in image_file:
                        wp_id = image_data.get('wp_id')
                        if wp_id and str(wp_id) not in new_image_ids:
                            new_image_ids.append(str(wp_id))
                
                # Pokud nemáme žádné nové obrázky, zkusíme najít na základě ID článku
                if not new_image_ids and 'original_article_id' in gallery_data:
                    article_id = str(gallery_data['original_article_id'])
                    
                    for image_file, image_data in image_map.items():
                        if 'original_article_id' in image_data and str(image_data['original_article_id']) == article_id:
                            wp_id = image_data.get('wp_id')
                            if wp_id and str(wp_id) not in new_image_ids:
                                new_image_ids.append(str(wp_id))
                
                # Pokud jsme nenašli žádné nové obrázky, zkusíme použít původní image_ids
                if not new_image_ids and 'image_ids' in gallery_data and gallery_data['image_ids']:
                    new_image_ids = gallery_data['image_ids']
                
                # Pokud stále nemáme žádné obrázky, nemůžeme opravit
                if not new_image_ids:
                    logging.warning(f"Post ID {post_id} ({article_title}): Nenalezeny žádné obrázky k přiřazení")
                    failed_count += 1
                    continue
                
                # Podívat se na současný počet obrázků a porovnat s novým počtem
                if original_image_count >= len(new_image_ids):
                    logging.info(f"Post ID {post_id} ({article_title}): Počet obrázků je již {original_image_count}, není potřeba opravovat")
                    already_ok_count += 1
                    continue
                
                # Aktualizovat metadata
                gallery_data['image_ids'] = new_image_ids
                gallery_data['image_count'] = len(new_image_ids)
                gallery_data['fixed_migration'] = True
                gallery_data['migration_timestamp'] = datetime.now().isoformat()
                
                # Aktualizovat databázi
                mysql_cursor.execute(f"""
                    UPDATE {wp_prefix}postmeta
                    SET meta_value = %s
                    WHERE meta_id = %s
                """, (json.dumps(gallery_data), meta_id))
                
                # Aktualizovat gallery shortcode v obsahu příspěvku
                new_gallery_shortcode = f'[gallery ids="{",".join(new_image_ids)}"]'
                
                # Nahradit všechny gallery shortcodes novým shortcodem
                for old_shortcode in gallery_shortcodes:
                    post_content = post_content.replace(old_shortcode, new_gallery_shortcode)
                
                # Aktualizovat obsah příspěvku
                mysql_cursor.execute(f"""
                    UPDATE {wp_prefix}posts
                    SET post_content = %s
                    WHERE ID = %s
                """, (post_content, post_id))
                
                # Commituji změny
                mysql_conn.commit()
                
                logging.info(f"Post ID {post_id} ({article_title}): Aktualizováno z {original_image_count} na {len(new_image_ids)} obrázků")
                updated_count += 1
                
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při opravě galerie pro post_id {gallery['post_id']}: {e}")
                import traceback
                logging.error(traceback.format_exc())
                failed_count += 1
        
        # Finální výpis
        logging.info(f"Oprava dokončena.")
        logging.info(f"Celkem aktualizováno: {updated_count}")
        logging.info(f"Již v pořádku: {already_ok_count}")
        logging.info(f"Selhalo: {failed_count}")
        
    except Exception as e:
        logging.error(f"Obecná chyba při opravě galerií: {e}")
        import traceback
        logging.error(traceback.format_exc())
    finally:
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    fix_gallery_entries()
