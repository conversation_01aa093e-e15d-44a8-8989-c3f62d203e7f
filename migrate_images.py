import logging
import os
import mysql.connector
from datetime import datetime
import re
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_GALERIE_POLOZKA,
    TBL_WP_POSTS, TBL_WP_POSTMETA,
    OLD_IMAGE_BASE_PATH, WP_UPLOADS_PATH, WP_SITE_URL,
    DEFAULT_WP_USER_ID
)
from utils import (
    load_mapping, save_mapping, copy_image_file,
    get_mime_type, format_wp_datetime, format_wp_datetime_gmt
)
import shutil

# Přidáme funkci pro kontrolu a zajištění existence cílového adres<PERSON>ře
def ensure_dir_exists(path):
    """<PERSON><PERSON><PERSON><PERSON>, že zadaný adres<PERSON>ř existuje, pokud ne, vytvo<PERSON><PERSON> ho."""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Chyba při vytv<PERSON><PERSON><PERSON><PERSON> ad<PERSON> {path}: {e}")
        return False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_images():
    logging.info("Spouštím migraci obrázků...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    image_map = load_mapping('image_map.json')
    processed_paths = set(image_map.keys())
    new_images_count = 0
    failed_images_count = 0

    try:
        # Nejprve zkontrolujeme, zda hledáme konkrétní obrázek (pro testování)
        search_image_pattern = os.environ.get('SEARCH_IMAGE_PATTERN')
        search_article_id = os.environ.get('SEARCH_ARTICLE_ID')
        
        if search_image_pattern:
            logging.info(f"Hledám obrázky podle vzoru: {search_image_pattern}")
        
        if search_article_id:
            logging.info(f"Hledám obrázky pro článek ID: {search_article_id}")
            
        # 1. Získat cesty k obrázkům z článků
        article_images_query = f"SELECT id_clanek, obrazek_src, cas_vlozeni FROM {TBL_CLANEK} WHERE obrazek_src IS NOT NULL AND obrazek_src != ''"
        
        if search_article_id:
            article_images_query += f" AND id_clanek = {search_article_id}"
            
        pg_cursor.execute(article_images_query)
        article_images = [(row[0], row[1], row[2]) for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(article_images)} obrázků v článcích.")

        # 2. Získat cesty k obrázkům z tabulky obrazek
        db_images_query = f"""
            SELECT id_obrazek, soubor, polozka_id 
            FROM {TBL_OBRAZEK} 
            WHERE soubor IS NOT NULL AND soubor != ''
        """
        
        if search_image_pattern:
            db_images_query += f" AND soubor LIKE '%{search_image_pattern}%'"
            
        if search_article_id:
            # Pokud hledáme obrázky pro konkrétní článek, získáme jeho unikatni_id
            pg_cursor.execute(f"SELECT unikatni_id FROM {TBL_CLANEK} WHERE id_clanek = %s", (search_article_id,))
            article_unique_id = pg_cursor.fetchone()
            if article_unique_id and article_unique_id[0]:
                db_images_query += f" AND polozka_id = '{article_unique_id[0]}'"
        
        pg_cursor.execute(db_images_query)
        db_images = [(row[0], row[1], row[2]) for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(db_images)} obrázků v tabulce {TBL_OBRAZEK}.")
        
        # Výpis obrázků podle polozka_id pro lepší přehled
        polozka_counts = {}
        for _, _, polozka_id in db_images:
            if polozka_id:
                polozka_counts[polozka_id] = polozka_counts.get(polozka_id, 0) + 1
        
        for polozka_id, count in polozka_counts.items():
            if count > 1:  # Jen výpis položek s více obrázky (potenciální galerie)
                # Zkusíme najít odpovídající článek
                pg_cursor.execute(f"SELECT id_clanek, nazev FROM {TBL_CLANEK} WHERE unikatni_id = %s", (polozka_id,))
                article = pg_cursor.fetchone()
                if article:
                    logging.info(f"Pro článek ID {article[0]} ('{article[1]}') nalezeno {count} obrázků.")
                else:
                    logging.info(f"Pro položku ID {polozka_id} nalezeno {count} obrázků, ale nebyl nalezen odpovídající článek.")

        # 3. Získat cesty k obrázkům z tabulky galerie_polozka
        gallery_images_query = f"SELECT id_galerie_polozka, soubor FROM {TBL_GALERIE_POLOZKA} WHERE soubor IS NOT NULL AND soubor != ''"
        
        if search_image_pattern:
            gallery_images_query += f" AND soubor LIKE '%{search_image_pattern}%'"
            
        pg_cursor.execute(gallery_images_query)
        gallery_images = [(row[0], row[1]) for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(gallery_images)} obrázků v tabulce {TBL_GALERIE_POLOZKA}.")

        # 4. Zpracovat obrázky z článků
        for article_id, image_src, created_date in article_images:
            if image_src in processed_paths:
                logging.info(f"Obrázek '{image_src}' již byl zpracován, přeskakuji.")
                continue

            # Sestavit cestu ke zdrojovému souboru
            src_path = os.path.join(OLD_IMAGE_BASE_PATH, image_src)
            
            # Zkontrolovat, zda soubor existuje
            if not os.path.exists(src_path):
                logging.warning(f"Zdrojový soubor '{src_path}' neexistuje, přeskakuji.")
                failed_images_count += 1
                continue

            # Vytvořit cílovou cestu ve WordPress uploads adresáři
            # Použít strukturu YYYY/MM/ podle data vložení článku
            if created_date:
                year_month = created_date.strftime('%Y/%m')
            else:
                year_month = datetime.now().strftime('%Y/%m')
            
            # Zachovat původní název souboru
            filename = os.path.basename(image_src)
            dest_rel_path = f"{year_month}/{filename}"
            dest_path = os.path.join(WP_UPLOADS_PATH, dest_rel_path)

            # Ujistit se, že cílový adresář existuje
            dest_dir = os.path.dirname(dest_path)
            if not ensure_dir_exists(dest_dir):
                logging.error(f"Nelze vytvořit cílový adresář pro {image_src}, přeskakuji.")
                failed_images_count += 1
                continue
                
            # Kopírovat soubor
            success = False
            try:
                # Nejprve zkusíme použít naši funkci copy_image_file
                if copy_image_file(src_path, dest_path):
                    logging.info(f"Úspěšně zkopírován soubor {src_path} do {dest_path}")
                    success = True
                else:
                    # Pokud selže, zkusíme použít shutil.copy2, který zachovává metadata
                    shutil.copy2(src_path, dest_path)
                    logging.info(f"Úspěšně zkopírován soubor pomocí shutil: {src_path} do {dest_path}")
                    success = True
            except Exception as e:
                logging.error(f"Chyba při kopírování souboru '{src_path}': {e}")
                failed_images_count += 1
                
            # Pokud se kopírování podařilo, vytvoříme záznam v databázi
            if success:
                try:
                    # Získat MIME typ
                    mime_type = get_mime_type(dest_path)
                    
                    # Připravit data pro wp_posts
                    post_date = format_wp_datetime(created_date)
                    post_date_gmt = format_wp_datetime_gmt(created_date)
                    post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
                    guid = f"{WP_SITE_URL}/wp-content/uploads/{dest_rel_path}"
                    
                    # Vložit do wp_posts
                    sql_posts = f"""
                        INSERT INTO {TBL_WP_POSTS}
                        (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
                        post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
                        to_ping, pinged, post_content_filtered)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(sql_posts, (
                        DEFAULT_WP_USER_ID, post_date, post_date_gmt, '', post_title, '',
                        'inherit', 'closed', 'closed', post_title.lower().replace(' ', '-'),
                        post_date, post_date_gmt, 0, guid, 0, 'attachment', mime_type, 0,
                        '', '', ''  # Hodnoty pro to_ping, pinged, post_content_filtered
                    ))
                    attachment_id = mysql_cursor.lastrowid
                    
                    # Vložit metadata
                    sql_meta1 = f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)"
                    mysql_cursor.execute(sql_meta1, (attachment_id, '_wp_attached_file', dest_rel_path))
                    
                    # Uložit mapování
                    image_map[image_src] = {
                        'wp_id': attachment_id,
                        'wp_path': dest_rel_path,
                        'wp_url': guid
                    }
                    processed_paths.add(image_src)
                    new_images_count += 1
                    
                    mysql_conn.commit()
                    logging.info(f"Obrázek '{image_src}' úspěšně migrován s ID: {attachment_id}")
                    
                except mysql.connector.Error as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při vkládání obrázku '{image_src}' do WP: {e}")
                    failed_images_count += 1

        # 5. Zpracovat obrázky z tabulky obrazek
        for image_id, image_file, polozka_id in db_images:
            if image_file in processed_paths:
                logging.info(f"Obrázek '{image_file}' již byl zpracován, přeskakuji.")
                continue
            
            # Podobný postup jako u obrázků z článků
            src_path = os.path.join(OLD_IMAGE_BASE_PATH, image_file)
            
            if not os.path.exists(src_path):
                logging.warning(f"Zdrojový soubor '{src_path}' neexistuje, přeskakuji.")
                failed_images_count += 1
                continue
            
            # Použít aktuální datum pro strukturu adresářů
            year_month = datetime.now().strftime('%Y/%m')
            filename = os.path.basename(image_file)
            dest_rel_path = f"{year_month}/{filename}"
            dest_path = os.path.join(WP_UPLOADS_PATH, dest_rel_path)
            
            # Ujistit se, že cílový adresář existuje
            dest_dir = os.path.dirname(dest_path)
            if not ensure_dir_exists(dest_dir):
                logging.error(f"Nelze vytvořit cílový adresář pro {image_file}, přeskakuji.")
                failed_images_count += 1
                continue
                
            # Kopírovat a vytvořit záznam v DB
            success = False
            try:
                # Nejprve zkusíme použít naši funkci copy_image_file
                if copy_image_file(src_path, dest_path):
                    logging.info(f"Úspěšně zkopírován soubor {src_path} do {dest_path}")
                    success = True
                else:
                    # Pokud selže, zkusíme použít shutil.copy2, který zachovává metadata
                    shutil.copy2(src_path, dest_path)
                    logging.info(f"Úspěšně zkopírován soubor pomocí shutil: {src_path} do {dest_path}")
                    success = True
            except Exception as e:
                logging.error(f"Chyba při kopírování souboru '{src_path}': {e}")
                failed_images_count += 1
                
            # Pokud se kopírování podařilo, vytvoříme záznam v databázi
            if success:
                try:
                    mime_type = get_mime_type(dest_path)
                    post_date = format_wp_datetime(datetime.now())
                    post_date_gmt = format_wp_datetime_gmt(datetime.now())
                    post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
                    guid = f"{WP_SITE_URL}/wp-content/uploads/{dest_rel_path}"
                    
                    mysql_cursor.execute(sql_posts, (
                        DEFAULT_WP_USER_ID, post_date, post_date_gmt, '', post_title, '',
                        'inherit', 'closed', 'closed', post_title.lower().replace(' ', '-'),
                        post_date, post_date_gmt, 0, guid, 0, 'attachment', mime_type, 0,
                        '', '', ''  # Hodnoty pro to_ping, pinged, post_content_filtered
                    ))
                    attachment_id = mysql_cursor.lastrowid
                    
                    mysql_cursor.execute(sql_meta1, (attachment_id, '_wp_attached_file', dest_rel_path))
                    
                    image_map[image_file] = {
                        'wp_id': attachment_id,
                        'wp_path': dest_rel_path,
                        'wp_url': guid
                    }
                    processed_paths.add(image_file)
                    new_images_count += 1
                    
                    mysql_conn.commit()
                    logging.info(f"Obrázek '{image_file}' úspěšně migrován s ID: {attachment_id}")
                    
                except mysql.connector.Error as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při vkládání obrázku '{image_file}' do WP: {e}")
                    failed_images_count += 1

        # 6. Zpracovat obrázky z tabulky galerie_polozka (podobně jako výše)
        for gallery_item_id, image_file in gallery_images:
            if image_file in processed_paths:
                logging.info(f"Obrázek '{image_file}' již byl zpracován, přeskakuji.")
                continue
            
            # Podobný postup jako u obrázků z článků a tabulky obrazek
            src_path = os.path.join(OLD_IMAGE_BASE_PATH, image_file)
            
            if not os.path.exists(src_path):
                logging.warning(f"Zdrojový soubor '{src_path}' neexistuje, přeskakuji.")
                failed_images_count += 1
                continue
            
            year_month = datetime.now().strftime('%Y/%m')
            filename = os.path.basename(image_file)
            dest_rel_path = f"{year_month}/{filename}"
            dest_path = os.path.join(WP_UPLOADS_PATH, dest_rel_path)
            
            # Ujistit se, že cílový adresář existuje
            dest_dir = os.path.dirname(dest_path)
            if not ensure_dir_exists(dest_dir):
                logging.error(f"Nelze vytvořit cílový adresář pro {image_file}, přeskakuji.")
                failed_images_count += 1
                continue
                
            # Kopírovat a vytvořit záznam v DB
            success = False
            try:
                # Nejprve zkusíme použít naši funkci copy_image_file
                if copy_image_file(src_path, dest_path):
                    logging.info(f"Úspěšně zkopírován soubor {src_path} do {dest_path}")
                    success = True
                else:
                    # Pokud selže, zkusíme použít shutil.copy2, který zachovává metadata
                    shutil.copy2(src_path, dest_path)
                    logging.info(f"Úspěšně zkopírován soubor pomocí shutil: {src_path} do {dest_path}")
                    success = True
            except Exception as e:
                logging.error(f"Chyba při kopírování souboru '{src_path}': {e}")
                failed_images_count += 1
                
            # Pokud se kopírování podařilo, vytvoříme záznam v databázi
            if success:
                try:
                    mime_type = get_mime_type(dest_path)
                    post_date = format_wp_datetime(datetime.now())
                    post_date_gmt = format_wp_datetime_gmt(datetime.now())
                    post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
                    guid = f"{WP_SITE_URL}/wp-content/uploads/{dest_rel_path}"
                    
                    mysql_cursor.execute(sql_posts, (
                        DEFAULT_WP_USER_ID, post_date, post_date_gmt, '', post_title, '',
                        'inherit', 'closed', 'closed', post_title.lower().replace(' ', '-'),
                        post_date, post_date_gmt, 0, guid, 0, 'attachment', mime_type, 0,
                        '', '', ''  # Hodnoty pro to_ping, pinged, post_content_filtered
                    ))
                    attachment_id = mysql_cursor.lastrowid
                    
                    mysql_cursor.execute(sql_meta1, (attachment_id, '_wp_attached_file', dest_rel_path))
                    
                    image_map[image_file] = {
                        'wp_id': attachment_id,
                        'wp_path': dest_rel_path,
                        'wp_url': guid
                    }
                    processed_paths.add(image_file)
                    new_images_count += 1
                    
                    mysql_conn.commit()
                    logging.info(f"Obrázek '{image_file}' úspěšně migrován s ID: {attachment_id}")
                    
                except mysql.connector.Error as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při vkládání obrázku '{image_file}' do WP: {e}")
                    failed_images_count += 1

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_images: {e}")
    finally:
        # Uložení mapování - opraveno, aby fungovalo i pro větší množství dat
        try:
            # Průběžné ukládání je důležité - ukládáme každých 100 obrázků
            if len(image_map) > 0 and len(image_map) % 100 == 0:
                save_mapping(image_map, 'image_map.json')
                logging.info(f"Průběžně uloženo mapování po {len(image_map)} obrázcích")
            
            # Závěrečné uložení
            save_mapping(image_map, 'image_map.json')
            logging.info(f"Mapování úspěšně uloženo, počet záznamů: {len(image_map)}")
        except Exception as save_error:
            logging.error(f"Chyba při ukládání mapování: {save_error}")
            
            # Nouzové ukládání po částech, pokud je soubor příliš velký
            try:
                logging.info("Pokus o nouzové ukládání po částech...")
                chunk_size = 1000
                item_count = len(image_map)
                chunks = (item_count + chunk_size - 1) // chunk_size  # Zaokrouhlení nahoru
                
                for i in range(chunks):
                    start = i * chunk_size
                    end = min((i + 1) * chunk_size, item_count)
                    
                    # Vytvořit podmnožinu mapování
                    chunk_map = dict(list(image_map.items())[start:end])
                    
                    # Uložit část
                    save_mapping(chunk_map, f'image_map_part_{i+1}.json')
                    logging.info(f"Uložena část {i+1}/{chunks} s {len(chunk_map)} záznamy")
            except Exception as chunk_error:
                logging.error(f"Nouzové ukládání selhalo: {chunk_error}")
        
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace obrázků dokončena. Zpracováno celkem {new_images_count} nových obrázků. Selhalo: {failed_images_count}.")

if __name__ == "__main__":
    migrate_images()
