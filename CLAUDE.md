# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands
- **Run full migration**: `python run_migration.py`
- **Run single component**: `python migrate_categories.py`, `python migrate_users.py`, etc.
- **Verify execution**: Check logs in `logs/` directory

## Code Style Guidelines
- **Imports**: Standard library first, then third-party, then local modules
- **Formatting**: 4-space indentation, 120 character line limit
- **Naming**: snake_case for functions/variables, TBL_PREFIX for table constants
- **Types**: No explicit type annotations, follow existing pattern
- **Error Handling**: Use try/except with specific exceptions, log errors
- **Logging**: Use existing logging setup, maintain message style
- **Database Access**: Use db_connectors.py functions for connections
- **Transactions**: Begin transaction, commit explicitly, rollback on error
- **File Handling**: Use utils.py helper functions for file operations
- **Documentation**: Docstrings in Czech, maintain existing format

## Project Structure
- Configuration in config.py and .env
- Utility functions in utils.py
- Database connections in db_connectors.py
- Migration scripts are independent but follow common patterns

## Testing
- No formal testing framework
- Verify migrations by checking logs in `logs/` directory
- Inspect mapping files in `mappings/` to confirm data migration progress