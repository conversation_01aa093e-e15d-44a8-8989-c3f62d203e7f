#!/bin/bash

# Skript pro spuštění univerzální opravy všech galerií

echo "Spouštím univerzální opravu všech galerií..."
echo ""

# <PERSON><PERSON><PERSON><PERSON>, zda je Python nainstalován
if ! command -v python3 &> /dev/null; then
    echo "Python 3 není nainstal<PERSON>n. Prosím, nainstalujte Python 3 a zkuste to znovu."
    exit 1
fi

# Kontrol<PERSON>, zda existuje soubor s opravou
if [ ! -f "fix_all_galleries.py" ]; then
    echo "Soubor fix_all_galleries.py nebyl nalezen v aktuálním adresáři."
    exit 1
fi

# Kontrola, zda jsou nainstalované potřebné bal<PERSON>ky
MISSING_PACKAGES=0

python3 -c "import mysql.connector" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Balíček mysql-connector-python není nainstal<PERSON>n."
    MISSING_PACKAGES=1
fi

python3 -c "import psycopg2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Balíček psycopg2 není nainstal<PERSON>n."
    MISSING_PACKAGES=1
fi

# Pokud chybí balíčky, zobrazit nápovědu
if [ $MISSING_PACKAGES -eq 1 ]; then
    echo ""
    echo "Chybí potřebné Python balíčky. Je několik způsobů, jak je nainstalovat:"
    echo ""
    echo "1. Použijte systémové balíčky (doporučeno pro Debian/Ubuntu):"
    echo "   sudo apt install python3-mysql.connector python3-psycopg2"
    echo ""
    echo "2. Vytvořte virtuální prostředí a nainstalujte balíčky tam:"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install mysql-connector-python psycopg2-binary"
    echo "   # A poté spusťte skript z tohoto prostředí:"
    echo "   ./fix_all_galleries.sh"
    echo ""
    echo "3. Pokud máte nainstalovaný pipx, můžete použít:"
    echo "   pipx run --spec mysql-connector-python --spec psycopg2-binary python fix_all_galleries.py"
    echo ""
    exit 1
fi

# Nastavení oprávnění pro spuštění
chmod +x fix_all_galleries.py

# Zpracování parametrů
DRY_RUN=""
VERBOSE=""
LOG_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN="--dry-run"
            shift
            ;;
        --verbose)
            VERBOSE="--verbose"
            shift
            ;;
        --log-file=*)
            LOG_FILE="--log-file=${1#*=}"
            shift
            ;;
        *)
            echo "Neznámý parametr: $1"
            echo "Použití: $0 [--dry-run] [--verbose] [--log-file=cesta]"
            exit 1
            ;;
    esac
done

# Spuštění skriptu pro opravu
python3 fix_all_galleries.py $DRY_RUN $VERBOSE $LOG_FILE

# Kontrola, zda skript proběhl úspěšně
if [ $? -ne 0 ]; then
    echo ""
    echo "Oprava selhala. Podívejte se do logů pro více informací."
    exit 1
fi

echo ""
echo "Oprava dokončena. Podívejte se do logů pro detail informací o opravě."
echo "Log soubor: logs/fix_all_galleries_*.log"
