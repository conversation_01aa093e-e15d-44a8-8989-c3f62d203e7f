# Kompletní Migrace a Oprava všech Obrázků

Tento dokument popisuje nový skript pro kompletní migraci dat ze SABRE databáze (PostgreSQL) do WordPress databáze (MySQL) včetně opravy všech problémů s obrázky a galeriemi.

## 🚀 Co skript řeší

Nový skript `run_complete_migration.sh` provádí následující kroky:

1. **Z<PERSON>ladn<PERSON> migraci** dat (kategorie, uživatelé, obrázky, články, galerie)
2. **Systematickou opravu všech galerií** (preferování větších obrázků, odstranění duplicit)
3. **Opravu obrázků v obsahu článků** (vložené obrázky v textu)

Tento skript spojuje existující migrační skripty a nástroje pro opravu do jedné komplexní p<PERSON>lou<PERSON>ti, k<PERSON><PERSON> zajistí úpln<PERSON> a správnou migraci všech dat.

## 📋 Použití

Spuštění kompletní migrace:

```bash
cd /home/<USER>/www/dumabyt/wp-content/themes/dumabyt/migration
./run_complete_migration.sh
```

### Dostupné parametry

```
Použití: ./run_complete_migration.sh [MOŽNOSTI]

Možnosti:
  -h, --help          Zobrazí tuto nápovědu
  -r, --reset         Resetuje WordPress databázi před migrací (POZOR: smaže všechna data)
  -f, --only-fixes    Přeskočí základní migraci a provede pouze opravy
  -i, --only-images   Provede pouze migraci a opravu obrázků
  -d, --dry-run       Provede testovací běh bez ukládání změn
  -y, --yes           Automaticky potvrdí všechny dotazy (neinteraktivní režim)
```

### Příklady použití

1. **Kompletní migrace s resetem databáze**:
   ```bash
   ./run_complete_migration.sh --reset
   ```

2. **Pouze oprava galerií a obrázků** (pokud už byla provedena základní migrace):
   ```bash
   ./run_complete_migration.sh --only-fixes
   ```

3. **Testovací běh bez ukládání změn**:
   ```bash
   ./run_complete_migration.sh --dry-run
   ```

4. **Neinteraktivní režim** (bez dotazů):
   ```bash
   ./run_complete_migration.sh --reset --yes
   ```

## 📊 Postup migrace

1. **Kontrola závislostí**
   - Skript ověří a případně nainstaluje všechny potřebné Python knihovny

2. **Základní migrace**
   - Spuštění `run_migration.py` pro migraci kategorií, uživatelů, obrázků, článků a základních galerií

3. **Oprava galerií**
   - Spuštění `fix_all_galleries_systematic.py` pro systematickou opravu galerií
   - Preferování větších kvalitních obrázků a odstranění duplicit
   - Mapování obrázků mezi PostgreSQL a WordPress

4. **Oprava obrázků v textu**
   - Spuštění `fix_post_content_images.py` pro opravu obrázků vložených v obsahu článků
   - Kontrola a nahrazení cest k obrázkům v HTML obsahu

5. **Shrnutí a log**
   - Vytvoření podrobného logovacího souboru
   - Zobrazení celkové doby trvání a výsledku

## 📝 Logování

Skript vytváří podrobné logy v adresáři `logs/`:

- **Hlavní log**:
  - `logs/run_complete_migration_YYYYMMDD_HHMMSS.log`

- **Dílčí logy**:
  - `logs/complete_migration_YYYYMMDD_HHMMSS.log` - Log Python skriptu
  - `logs/fix_all_galleries_systematic_YYYYMMDD_HHMMSS.log` - Log opravy galerií
  - `logs/fix_post_content_images_YYYYMMDD_HHMMSS.log` - Log opravy obrázků v obsahu

## ⚙️ Implementační detaily

Skript `run_complete_migration.sh` je Bash wrapper, který spouští Python skript `migrate_and_fix_all.py`. Ten provádí:

1. **Koordinaci** jednotlivých kroků migrace
2. **Zpracování chyb** a možnost pokračovat i přes částečné selhání
3. **Podrobné logování** celého procesu

## 🚨 Poznámky a varování

- **Reset databáze**: Použití parametru `--reset` způsobí smazání všech existujících dat ve WordPress databázi!
- **Doba trvání**: Kompletní migrace může trvat několik hodin, v závislosti na množství dat
- **Přerušení**: V případě přerušení lze proces obnovit pomocí parametru `--only-fixes`, který přeskočí základní migraci
- **Kontrola výsledků**: Po migraci je vhodné zkontrolovat výsledky a případné problémy řešit manuálně
