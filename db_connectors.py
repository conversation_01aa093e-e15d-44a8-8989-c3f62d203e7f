import psycopg2
import mysql.connector
from config import PG_CONFIG, MYSQL_CONFIG
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_pg_connection():
    """Vrátí připojení k PostgreSQL."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        logging.info("Úspěšně připojeno k PostgreSQL.")
        return conn
    except psycopg2.Error as e:
        logging.error(f"Chyba připojení k PostgreSQL: {e}")
        raise

def get_mysql_connection():
    """Vrátí připojení k MySQL."""
    try:
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        logging.info("Úspěšně připojeno k MySQL.")
        return conn
    except mysql.connector.Error as e:
        logging.error(f"Chyba připojení k MySQL: {e}")
        raise
