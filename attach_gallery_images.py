#!/usr/bin/env python3
import logging
import os
import sys
import json
from datetime import datetime
from db_connectors import get_mysql_connection
from config import (
    TBL_WP_POSTS, TBL_WP_POSTMETA
)

# Nastavení logování
LOG_FILE = os.path.join('logs', f'attach_gallery_images_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def attach_gallery_images_to_posts(dry_run=False):
    """
    Propojí obrázky galérií s příspěvky nastavením post_parent.
    Toto zajistí, že galerie vytvořené během migrace budou správně zobrazeny.
    
    Args:
        dry_run (bool): Pokud True, pouze simuluje změny bez skutečného zápisu
    
    Returns:
        tuple: (počet úspěšných příspěvků, počet obrázků, počet selhání)
    """
    logging.info("Spouštím propojení obrázků galérií s příspěvky...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    success_count = 0
    failed_count = 0
    total_images_attached = 0
    already_attached_count = 0
    
    try:
        # Najít všechny příspěvky s metadaty sabre_gallery
        mysql_cursor.execute(f"""
            SELECT post_id, meta_value 
            FROM {TBL_WP_POSTMETA} 
            WHERE meta_key = 'sabre_gallery'
        """)
        
        galleries = mysql_cursor.fetchall()
        total_galleries = len(galleries)
        logging.info(f"Nalezeno {total_galleries} příspěvků s metadaty 'sabre_gallery'.")
        
        # Pro každý příspěvek zpracovat jeho galerii
        for i, (post_id, meta_value) in enumerate(galleries, 1):
            try:
                # Dekódovat JSON metadata
                gallery_data = json.loads(meta_value)
                
                # Získat seznam ID obrázků
                image_ids = gallery_data.get('image_ids', [])
                if not image_ids:
                    logging.warning(f"Příspěvek ID {post_id} nemá žádné obrázky v metadatech galerie.")
                    continue
                
                # Informace o postupu
                if i % 50 == 0 or i == total_galleries:
                    logging.info(f"Zpracovávám {i}/{total_galleries} ({i/total_galleries*100:.1f}%)...")
                
                # Pro každý obrázek nastavit post_parent na ID příspěvku
                attached_count = 0
                already_attached = 0
                
                for image_id in image_ids:
                    try:
                        # Kontrola, zda je obrázek již připojen k příspěvku
                        mysql_cursor.execute(f"""
                            SELECT post_parent
                            FROM {TBL_WP_POSTS}
                            WHERE ID = %s AND post_type = 'attachment'
                        """, (image_id,))
                        
                        result = mysql_cursor.fetchone()
                        if not result:
                            logging.warning(f"Obrázek ID {image_id} neexistuje nebo není typu 'attachment'.")
                            continue
                        
                        current_parent = result[0]
                        
                        # Pokud již má správného rodiče, nemusíme nic měnit
                        if current_parent == post_id:
                            already_attached += 1
                            continue
                            
                        # Pokud je to dry run, pouze loguji, co bych dělal
                        if dry_run:
                            logging.info(f"[DRY RUN] Propojení obrázku ID {image_id} s příspěvkem ID {post_id}")
                            attached_count += 1
                            continue
                        
                        # Nastavit post_parent
                        mysql_cursor.execute(f"""
                            UPDATE {TBL_WP_POSTS}
                            SET post_parent = %s
                            WHERE ID = %s AND post_type = 'attachment'
                        """, (post_id, image_id))
                        
                        attached_count += 1
                        
                    except Exception as img_error:
                        logging.error(f"Chyba při propojování obrázku ID {image_id} s příspěvkem ID {post_id}: {img_error}")
                
                # Aktualizovat celkové počty
                total_images_attached += attached_count
                already_attached_count += already_attached
                
                if not dry_run:
                    mysql_conn.commit()
                    
                # Log výsledku
                if attached_count > 0:
                    logging.info(f"Příspěvek ID {post_id}: Propojeno {attached_count} obrázků")
                    success_count += 1
                
                if already_attached > 0:
                    logging.info(f"Příspěvek ID {post_id}: {already_attached} obrázků již bylo propojeno dříve")
                
            except json.JSONDecodeError:
                mysql_conn.rollback()
                logging.error(f"Neplatný JSON formát v metadatech galerie pro příspěvek ID {post_id}.")
                failed_count += 1
            except Exception as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při zpracování příspěvku ID {post_id}: {e}")
                import traceback
                logging.error(traceback.format_exc())
                failed_count += 1
        
        logging.info(f"Zpracování dokončeno.")
        logging.info(f"Celkem propojeno {total_images_attached} obrázků s {success_count} příspěvky.")
        logging.info(f"Nalezeno {already_attached_count} obrázků, které již byly správně propojeny.")
        logging.info(f"Počet selhání: {failed_count}")
        
        return (success_count, total_images_attached, failed_count)
        
    except Exception as e:
        logging.error(f"Obecná chyba v attach_gallery_images_to_posts: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return (success_count, total_images_attached, failed_count)
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Propojení obrázků galérií s příspěvky ve WordPress.')
    parser.add_argument('--dry-run', '-d', action='store_true', help='Pouze simulace bez skutečného zápisu změn')
    
    args = parser.parse_args()
    
    print("=== Propojovač obrázků galérií s příspěvky ===")
    print(f"Režim: {'Simulace (bez zápisu změn)' if args.dry_run else 'Ostrý běh (zapisuje změny)'}")
    print()
    
    success, images, failed = attach_gallery_images_to_posts(args.dry_run)
    
    print()
    print("=== Souhrn ===")
    print(f"Počet úspěšně zpracovaných příspěvků: {success}")
    print(f"Počet propojených obrázků: {images}")
    print(f"Počet selhání: {failed}")
    print()
    print(f"Log soubor: {LOG_FILE}")
