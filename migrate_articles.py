import logging
import mysql.connector
import re
from bs4 import BeautifulSoup
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    TBL_WP_TERM_RELATIONSHIPS, DEFAULT_WP_USER_ID
)
from utils import (
    load_mapping, save_mapping, generate_slug,
    format_wp_datetime, format_wp_datetime_gmt,
    extract_author_name, update_image_urls_in_content
)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_articles(batch_size=50, start_offset=0):
    logging.info("Spouštím migraci článků...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    category_map = load_mapping('category_map.json')
    user_map = load_mapping('user_map.json')
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')

    # Počítadla
    new_articles_count = 0
    failed_articles_count = 0
    processed_articles = set(article_map.keys())

    try:
        # Získat celkový počet článků
        pg_cursor.execute(f"SELECT COUNT(*) FROM {TBL_CLANEK}")
        total_articles = pg_cursor.fetchone()[0]
        logging.info(f"Celkový počet článků v databázi: {total_articles}")

        # Zpracovat články po dávkách
        offset = start_offset
        while True:
            pg_cursor.execute(f"""
                SELECT id_clanek, unikatni_id, rubrika_id, nazev, nahled, text,
                       zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src,
                       obrazek_alt, active_state, cislo_casopisu
                FROM {TBL_CLANEK}
                ORDER BY id_clanek
                LIMIT {batch_size} OFFSET {offset}
            """)
            
            articles = pg_cursor.fetchall()
            if not articles:
                break  # Konec dávek
            
            logging.info(f"Zpracovávám dávku {offset}-{offset + len(articles)} z {total_articles} článků.")
            
            for article in articles:
                (
                    id_clanek, unikatni_id, rubrika_id, nazev, nahled, text,
                    zobrazovat_od, aktualizovano, cas_vlozeni, autor, obrazek_src,
                    obrazek_alt, active_state, cislo_casopisu
                ) = article
                
                # Pokud už byl článek zpracován, přeskočit
                if str(id_clanek) in processed_articles:
                    logging.info(f"Článek ID {id_clanek} již byl zpracován, přeskakuji.")
                    continue
                
                try:
                    # 1. Připravit data pro WordPress post
                    
                    # Titulek
                    post_title = nazev or f"Článek {id_clanek}"
                    
                    # Slug
                    post_name = generate_slug(nazev)
                    
                    # Obsah
                    post_content = text or ""
                    
                    # Aktualizovat odkazy na obrázky v obsahu
                    post_content = update_image_urls_in_content(post_content, image_map)
                    
                    # Perex/excerpt
                    post_excerpt = nahled or ""
                    if post_excerpt:
                        # Odstranit HTML tagy z perexu
                        soup = BeautifulSoup(post_excerpt, 'html.parser')
                        post_excerpt = soup.get_text()
                    
                    # Datumy
                    post_date = format_wp_datetime(zobrazovat_od or cas_vlozeni)
                    post_date_gmt = format_wp_datetime_gmt(zobrazovat_od or cas_vlozeni)
                    post_modified = format_wp_datetime(aktualizovano or cas_vlozeni)
                    post_modified_gmt = format_wp_datetime_gmt(aktualizovano or cas_vlozeni)
                    
                    # Autor
                    post_author = DEFAULT_WP_USER_ID
                    if autor:
                        if autor in user_map:
                            post_author = user_map[autor]
                        else:
                            # Zkusit extrahovat jméno autora a najít mapování
                            author_name = extract_author_name(autor)
                            if author_name and author_name in user_map:
                                post_author = user_map[author_name]
                    
                    # Status
                    post_status = 'publish'
                    if active_state == 0:
                        post_status = 'draft'
                    
                    # 2. Vložit článek do WordPress
                    sql_posts = f"""
                        INSERT INTO {TBL_WP_POSTS}
                        (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
                        post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
                        to_ping, pinged, post_content_filtered)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    mysql_cursor.execute(sql_posts, (
                        post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
                        post_status, 'open', 'open', post_name, post_modified, post_modified_gmt,
                        0, f"http://dumabyt.test/?p=", 0, 'post', '', 0,
                        '', '', ''  # Hodnoty pro to_ping, pinged, post_content_filtered
                    ))
                    wp_post_id = mysql_cursor.lastrowid
                    
                    # Aktualizovat GUID s ID
                    mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET guid = %s WHERE ID = %s", 
                                        (f"http://dumabyt.test/?p={wp_post_id}", wp_post_id))
                    
                    # 3. Přiřadit kategorii
                    if rubrika_id and str(rubrika_id) in category_map:
                        wp_term_id = category_map[str(rubrika_id)]
                        sql_term_rel = f"""
                            INSERT INTO {TBL_WP_TERM_RELATIONSHIPS}
                            (object_id, term_taxonomy_id, term_order)
                            VALUES (%s, %s, 0)
                        """
                        mysql_cursor.execute(sql_term_rel, (wp_post_id, wp_term_id))
                        logging.info(f"Článku ID {wp_post_id} přiřazena kategorie ID {wp_term_id}")
                    
                    # 4. Nastavit featured image - najít správný úvodní obrázek
                    featured_image_set = False

                    def find_image_in_mapping(filename):
                        """Najde obrázek v mapování podle názvu souboru s pokročilým hledáním"""
                        if not filename:
                            return None

                        # 1. Nejdříve zkusit přímé hledání
                        if filename in image_map:
                            return image_map[filename]

                        # 2. Hledat podle názvu souboru v cestách
                        for path, info in image_map.items():
                            if path.endswith('/' + filename) or path == filename:
                                return info

                        # 3. Pokročilé hledání podle částečné shody (stejná logika jako v update_image_urls_in_content)
                        import re

                        # Normalizovat název souboru pro hledání
                        base_name = filename

                        # Odstranit příponu
                        base_name = re.sub(r'\.[^.]+$', '', base_name)

                        # Odstranit hash na konci (např. -680a1f086e7e4)
                        base_name = re.sub(r'-[a-f0-9]{10,}$', '', base_name)

                        # Odstranit rozměry (např. -1050x600)
                        base_name = re.sub(r'-\d+x\d+$', '', base_name)

                        # Odstranit číselné prefixy (např. "2-")
                        base_name = re.sub(r'^\d+-', '', base_name)

                        # Hledat v mapování podle částečné shody
                        for map_path, info in image_map.items():
                            if isinstance(info, dict) and 'wp_url' in info:
                                map_filename = map_path.split('/')[-1]  # Získat název souboru z cesty
                                map_base = re.sub(r'\.[^.]+$', '', map_filename)  # Odstranit příponu

                                # Zkusit různé varianty shody
                                if (base_name in map_base or map_base in base_name or
                                    base_name.replace('-', '') == map_base.replace('-', '') or
                                    base_name.replace('(', '').replace(')', '') == map_base.replace('(', '').replace(')', '')):
                                    logging.info(f"Nalezena shoda pro featured image: {filename} -> {map_filename}")
                                    return info

                        return None

                    # Nejdříve zkusit najít úvodní obrázek z tabulky obrazek (typ = 0)
                    if unikatni_id:
                        pg_cursor.execute(f"""
                            SELECT soubor FROM {TBL_OBRAZEK}
                            WHERE polozka_id = %s AND typ = 0 AND active_state = 1
                            ORDER BY priorita DESC, id_obrazek
                            LIMIT 1
                        """, (unikatni_id,))

                        featured_image_result = pg_cursor.fetchone()
                        if featured_image_result:
                            featured_image_filename = featured_image_result[0]

                            # Najít obrázek v mapování podle názvu souboru
                            image_info = find_image_in_mapping(featured_image_filename)
                            if image_info:
                                attachment_id = image_info['wp_id']

                                # Nastavit featured image
                                sql_thumbnail = f"""
                                    INSERT INTO {TBL_WP_POSTMETA}
                                    (post_id, meta_key, meta_value)
                                    VALUES (%s, %s, %s)
                                """
                                mysql_cursor.execute(sql_thumbnail, (wp_post_id, '_thumbnail_id', attachment_id))

                                # Přiřadit obrázek k článku
                                mysql_cursor.execute(f"""
                                    UPDATE {TBL_WP_POSTS}
                                    SET post_parent = %s
                                    WHERE ID = %s AND post_parent = 0
                                """, (wp_post_id, attachment_id))

                                featured_image_set = True
                                logging.info(f"Článku ID {wp_post_id} nastaven featured image ID {attachment_id} (úvodní obrázek typ=0: {featured_image_filename})")

                    # Pokud nebyl nalezen úvodní obrázek typ=0, zkusit obrazek_src z článku
                    if not featured_image_set and obrazek_src:
                        image_info = find_image_in_mapping(obrazek_src)
                        if image_info:
                            attachment_id = image_info['wp_id']

                            # Nastavit featured image
                            sql_thumbnail = f"""
                                INSERT INTO {TBL_WP_POSTMETA}
                                (post_id, meta_key, meta_value)
                                VALUES (%s, %s, %s)
                            """
                            mysql_cursor.execute(sql_thumbnail, (wp_post_id, '_thumbnail_id', attachment_id))

                            # Přiřadit obrázek k článku
                            mysql_cursor.execute(f"""
                                UPDATE {TBL_WP_POSTS}
                                SET post_parent = %s
                                WHERE ID = %s AND post_parent = 0
                            """, (wp_post_id, attachment_id))

                            featured_image_set = True
                            logging.info(f"Článku ID {wp_post_id} nastaven featured image ID {attachment_id} (obrazek_src: {obrazek_src})")

                    # Pokud stále není nastaven, zkusit první obrázek z galerie (typ = 1)
                    if not featured_image_set and unikatni_id:
                        pg_cursor.execute(f"""
                            SELECT soubor FROM {TBL_OBRAZEK}
                            WHERE polozka_id = %s AND typ = 1 AND active_state = 1
                            ORDER BY priorita DESC, id_obrazek
                            LIMIT 1
                        """, (unikatni_id,))

                        gallery_image_result = pg_cursor.fetchone()
                        if gallery_image_result:
                            gallery_image_filename = gallery_image_result[0]

                            # Najít obrázek v mapování podle názvu souboru
                            image_info = find_image_in_mapping(gallery_image_filename)
                            if image_info:
                                attachment_id = image_info['wp_id']

                                # Nastavit featured image
                                sql_thumbnail = f"""
                                    INSERT INTO {TBL_WP_POSTMETA}
                                    (post_id, meta_key, meta_value)
                                    VALUES (%s, %s, %s)
                                """
                                mysql_cursor.execute(sql_thumbnail, (wp_post_id, '_thumbnail_id', attachment_id))

                                # Přiřadit obrázek k článku
                                mysql_cursor.execute(f"""
                                    UPDATE {TBL_WP_POSTS}
                                    SET post_parent = %s
                                    WHERE ID = %s AND post_parent = 0
                                """, (wp_post_id, attachment_id))

                                featured_image_set = True
                                logging.info(f"Článku ID {wp_post_id} nastaven featured image ID {attachment_id} (první z galerie: {gallery_image_filename})")

                    if not featured_image_set:
                        logging.warning(f"Pro článek ID {wp_post_id} nebyl nalezen žádný vhodný featured image")
                    
                    # 5. Uložit další metadata
                    # Poznámka: Sloupec 'zdroj' neexistuje v tabulce, proto je zakomentován
                    # if zdroj:
                    #     mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                    #                         (wp_post_id, 'sabre_zdroj', zdroj))
                    
                    if cislo_casopisu:
                        mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                            (wp_post_id, 'sabre_cislo_casopisu', cislo_casopisu))
                    
                    # Uložit původní ID pro reference
                    mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                        (wp_post_id, 'sabre_id', id_clanek))
                    
                    if unikatni_id:
                        mysql_cursor.execute(f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
                                            (wp_post_id, 'sabre_unikatni_id', unikatni_id))
                    
                    # Commit transakce
                    mysql_conn.commit()
                    
                    # Aktualizovat mapování
                    article_map[str(id_clanek)] = wp_post_id
                    processed_articles.add(str(id_clanek))
                    new_articles_count += 1
                    
                    logging.info(f"Článek ID {id_clanek} úspěšně migrován s ID: {wp_post_id}")
                    
                except mysql.connector.Error as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při vkládání článku ID {id_clanek} do WP: {e}")
                    failed_articles_count += 1
                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"Obecná chyba při zpracování článku ID {id_clanek}: {e}")
                    failed_articles_count += 1
            
            # Uložit mapování po každé dávce
            save_mapping(article_map, 'article_map.json')
            
            # Posunout offset pro další dávku
            offset += batch_size
            
            logging.info(f"Dokončena dávka {offset-batch_size}-{offset}. Zpracováno {new_articles_count} nových článků.")
            
            # Volitelně: Přerušit po určitém počtu dávek pro testování
            # if offset > 1000:
            #     break

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_articles: {e}")
    finally:
        save_mapping(article_map, 'article_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace článků dokončena. Zpracováno celkem {new_articles_count} nových článků. Selhalo: {failed_articles_count}.")

if __name__ == "__main__":
    migrate_articles()
