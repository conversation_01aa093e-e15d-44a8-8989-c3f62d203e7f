#!/usr/bin/env python3
"""
Script pro opravu obrázků v existujících článcích:
1. Aktualizuje odkazy na obrázky v obsahu článků
2. Opraví featured images pomoc<PERSON> lepš<PERSON> logiky hled<PERSON>
"""

import logging
import mysql.connector
import psycopg2
import re
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA
)
from utils import load_mapping, update_image_urls_in_content

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def find_image_in_mapping_advanced(filename, image_map):
    """Najde obrázek v mapování podle názvu souboru s pokročilým hledáním"""
    if not filename:
        return None

    # 1. Nejdříve zkusit přímé hledání
    if filename in image_map:
        return image_map[filename]

    # 2. Hledat podle názvu souboru v cest<PERSON>ch
    for path, info in image_map.items():
        if path.endswith('/' + filename) or path == filename:
            return info

    # 3. Pokročilé hledání podle částečné shody
    # Normalizovat název souboru pro hledání
    base_name = filename
    
    # Odstranit příponu
    base_name = re.sub(r'\.[^.]+$', '', base_name)
    
    # Odstranit hash na konci (např. -680a1f086e7e4)
    base_name = re.sub(r'-[a-f0-9]{10,}$', '', base_name)
    
    # Odstranit rozměry (např. -1050x600)
    base_name = re.sub(r'-\d+x\d+$', '', base_name)
    
    # Odstranit číselné prefixy (např. "2-")
    base_name = re.sub(r'^\d+-', '', base_name)
    
    # Hledat v mapování podle částečné shody
    for map_path, info in image_map.items():
        if isinstance(info, dict) and 'wp_url' in info:
            map_filename = map_path.split('/')[-1]  # Získat název souboru z cesty
            map_base = re.sub(r'\.[^.]+$', '', map_filename)  # Odstranit příponu
            
            # Zkusit různé varianty shody
            if (base_name in map_base or map_base in base_name or 
                base_name.replace('-', '') == map_base.replace('-', '') or
                base_name.replace('(', '').replace(')', '') == map_base.replace('(', '').replace(')', '')):
                logging.info(f"Nalezena shoda pro obrázek: {filename} -> {map_filename}")
                return info

    return None

def fix_article_images(batch_size=50, start_offset=0):
    """Opraví obrázky v existujících článcích"""
    logging.info("Spouštím opravu obrázků v článcích...")
    
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')
    
    logging.info(f"Načteno {len(image_map)} obrázků v mapování")
    logging.info(f"Načteno {len(article_map)} článků v mapování")

    # Počítadla
    updated_content_count = 0
    updated_featured_count = 0
    failed_count = 0

    try:
        # Získat všechny WordPress články
        mysql_cursor.execute(f"""
            SELECT ID, post_content 
            FROM {TBL_WP_POSTS} 
            WHERE post_type = 'post' 
            AND post_status = 'publish'
            ORDER BY ID
        """)
        
        wp_articles = mysql_cursor.fetchall()
        total_articles = len(wp_articles)
        logging.info(f"Nalezeno {total_articles} WordPress článků k aktualizaci")

        # Zpracovat články po dávkách
        for i in range(start_offset, total_articles, batch_size):
            batch = wp_articles[i:i+batch_size]
            logging.info(f"Zpracovávám dávku {i}-{i+len(batch)} z {total_articles} článků")
            
            for wp_post_id, post_content in batch:
                try:
                    # 1. Aktualizovat obrázky v obsahu
                    if post_content and 'obrazek/' in post_content:
                        updated_content = update_image_urls_in_content(post_content, image_map)
                        
                        if updated_content != post_content:
                            # Aktualizovat obsah v databázi
                            mysql_cursor.execute(f"""
                                UPDATE {TBL_WP_POSTS} 
                                SET post_content = %s 
                                WHERE ID = %s
                            """, (updated_content, wp_post_id))
                            
                            updated_content_count += 1
                            logging.info(f"Aktualizován obsah článku ID {wp_post_id}")

                    # 2. Zkusit opravit featured image, pokud není nastaven nebo je to fallback
                    mysql_cursor.execute(f"""
                        SELECT meta_value 
                        FROM {TBL_WP_POSTMETA} 
                        WHERE post_id = %s AND meta_key = '_thumbnail_id'
                    """, (wp_post_id,))
                    
                    current_featured = mysql_cursor.fetchone()
                    
                    # Pokud není featured image nebo je to fallback, zkusit najít lepší
                    fallback_ids = ['48111', '47399', '62680', '70821', '5177', '4465', '27887', '27819']
                    if not current_featured or str(current_featured[0]) in fallback_ids:
                        # Najít původní článek v PostgreSQL
                        mysql_cursor.execute(f"""
                            SELECT meta_value 
                            FROM {TBL_WP_POSTMETA} 
                            WHERE post_id = %s AND meta_key = 'sabre_unikatni_id'
                        """, (wp_post_id,))
                        
                        sabre_id_result = mysql_cursor.fetchone()
                        if sabre_id_result:
                            unikatni_id = sabre_id_result[0]
                            
                            # Zkusit najít lepší featured image
                            new_featured_id = None
                            fallback_filenames = ['poutak-md.jpg', 'poutak.jpg', '700.jpg', 'poutak-md-02.jpg']

                            # Nejdříve zkusit úvodní obrázek (typ = 0), ale pouze pokud není fallback
                            pg_cursor.execute(f"""
                                SELECT soubor FROM {TBL_OBRAZEK}
                                WHERE polozka_id = %s AND typ = 0 AND active_state = 1
                                ORDER BY priorita DESC, id_obrazek
                                LIMIT 1
                            """, (unikatni_id,))

                            featured_result = pg_cursor.fetchone()
                            if featured_result:
                                featured_filename = featured_result[0]
                                # Pouze použít, pokud to není fallback obrázek
                                if featured_filename.lower() not in [f.lower() for f in fallback_filenames]:
                                    image_info = find_image_in_mapping_advanced(featured_filename, image_map)
                                    if image_info:
                                        new_featured_id = image_info['wp_id']
                                        logging.info(f"Nalezen lepší featured image pro článek {wp_post_id}: {featured_filename}")

                            # Pokud nenalezen nebo byl fallback, zkusit první z galerie (typ = 1)
                            if not new_featured_id:
                                pg_cursor.execute(f"""
                                    SELECT soubor FROM {TBL_OBRAZEK}
                                    WHERE polozka_id = %s AND typ = 1 AND active_state = 1
                                    ORDER BY priorita DESC, id_obrazek
                                    LIMIT 10
                                """, (unikatni_id,))

                                gallery_results = pg_cursor.fetchall()
                                for gallery_result in gallery_results:
                                    gallery_filename = gallery_result[0]
                                    # Přeskočit fallback obrázky
                                    if gallery_filename.lower() not in [f.lower() for f in fallback_filenames]:
                                        image_info = find_image_in_mapping_advanced(gallery_filename, image_map)
                                        if image_info:
                                            new_featured_id = image_info['wp_id']
                                            logging.info(f"Nalezen featured image z galerie pro článek {wp_post_id}: {gallery_filename}")
                                            break
                            
                            # Aktualizovat featured image, pokud byl nalezen lepší
                            if new_featured_id and str(new_featured_id) != str(current_featured[0] if current_featured else None):
                                if current_featured:
                                    # Aktualizovat existující
                                    mysql_cursor.execute(f"""
                                        UPDATE {TBL_WP_POSTMETA} 
                                        SET meta_value = %s 
                                        WHERE post_id = %s AND meta_key = '_thumbnail_id'
                                    """, (new_featured_id, wp_post_id))
                                else:
                                    # Vložit nový
                                    mysql_cursor.execute(f"""
                                        INSERT INTO {TBL_WP_POSTMETA} 
                                        (post_id, meta_key, meta_value) 
                                        VALUES (%s, %s, %s)
                                    """, (wp_post_id, '_thumbnail_id', new_featured_id))
                                
                                updated_featured_count += 1
                                logging.info(f"Aktualizován featured image článku ID {wp_post_id} na {new_featured_id}")

                    # Commit změny pro tento článek
                    mysql_conn.commit()
                    
                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při zpracování článku ID {wp_post_id}: {e}")
                    failed_count += 1

            logging.info(f"Dokončena dávka {i}-{i+len(batch)}")

    except Exception as e:
        logging.error(f"Obecná chyba: {e}")
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        
        logging.info(f"Oprava dokončena:")
        logging.info(f"  - Aktualizován obsah: {updated_content_count} článků")
        logging.info(f"  - Aktualizován featured image: {updated_featured_count} článků")
        logging.info(f"  - Selhalo: {failed_count} článků")

if __name__ == "__main__":
    fix_article_images()
