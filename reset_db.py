#!/usr/bin/env python3
import logging
import mysql.connector
from db_connectors import get_mysql_connection
from config import (
    TBL_WP_POSTS, TBL_WP_POSTMETA, TBL_WP_TERM_RELATIONSHIPS,
    DEFAULT_WP_USER_ID, WP_TABLE_PREFIX
)
import os
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def reset_database():
    """
    Resetuje WordPress databázi - odstraní všechny migrované položky
    a vyčistí soubory s mapováním.
    """
    logging.info("Spouštím reset WordPress databáze...")
    
    # Připojení k MySQL
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # 1. <PERSON><PERSON>t původní uživatele a administrátora
        mysql_cursor.execute(f"SELECT ID FROM {WP_TABLE_PREFIX}users WHERE ID <= {DEFAULT_WP_USER_ID}")
        admin_ids = [row[0] for row in mysql_cursor.fetchall()]
        admin_ids_str = ",".join(str(id) for id in admin_ids)
        
        # 2. Odstranit migrované příspěvky (posts)
        # Zjistit ID příspěvků, které byly migrovány
        mysql_cursor.execute(f"""
            SELECT post_id FROM {TBL_WP_POSTMETA} 
            WHERE meta_key = 'sabre_id'
        """)
        migrated_post_ids = [row[0] for row in mysql_cursor.fetchall()]
        
        if migrated_post_ids:
            migrated_ids_str = ",".join(str(id) for id in migrated_post_ids)
            
            # Odstranit vztahy mezi příspěvky a taxonomií
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_TERM_RELATIONSHIPS}
                WHERE object_id IN ({migrated_ids_str})
            """)
            logging.info(f"Odstraněno {mysql_cursor.rowcount} záznamů z tabulky term_relationships")
            
            # Odstranit metadata příspěvků
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTMETA}
                WHERE post_id IN ({migrated_ids_str})
            """)
            logging.info(f"Odstraněno {mysql_cursor.rowcount} záznamů z tabulky postmeta")
            
            # Odstranit příspěvky
            mysql_cursor.execute(f"""
                DELETE FROM {TBL_WP_POSTS}
                WHERE ID IN ({migrated_ids_str})
            """)
            logging.info(f"Odstraněno {mysql_cursor.rowcount} příspěvků z tabulky posts")
        else:
            logging.info("Nenalezeny žádné migrované příspěvky.")
        
        # 3. Odstranit všechny obrázky a přílohy (attachments)
        mysql_cursor.execute(f"""
            DELETE FROM {TBL_WP_POSTS}
            WHERE post_type = 'attachment'
        """)
        logging.info(f"Odstraněno {mysql_cursor.rowcount} příloh z tabulky posts")
        
        # 4. Resetovat kategorie - ponechat pouze výchozí
        mysql_cursor.execute(f"""
            DELETE FROM {WP_TABLE_PREFIX}terms
            WHERE term_id NOT IN (
                SELECT term_id FROM {WP_TABLE_PREFIX}term_taxonomy 
                WHERE taxonomy = 'category' AND term_id = 1
            )
        """)
        logging.info(f"Odstraněno {mysql_cursor.rowcount} termínů z tabulky terms")
        
        mysql_cursor.execute(f"""
            DELETE FROM {WP_TABLE_PREFIX}term_taxonomy
            WHERE taxonomy = 'category' AND term_id > 1
        """)
        logging.info(f"Odstraněno {mysql_cursor.rowcount} kategorií z tabulky term_taxonomy")
        
        mysql_cursor.execute(f"""
            DELETE FROM {WP_TABLE_PREFIX}termmeta
            WHERE term_id NOT IN (
                SELECT term_id FROM {WP_TABLE_PREFIX}term_taxonomy
            )
        """)
        logging.info(f"Odstraněno {mysql_cursor.rowcount} meta údajů z tabulky termmeta")
        
        # Commit změn
        mysql_conn.commit()
        
        # 5. Vyčistit soubory s mapováním
        mapping_files = ['article_map.json', 'category_map.json', 'gallery_map.json', 'image_map.json', 'user_map.json']
        for file_name in mapping_files:
            file_path = os.path.join('mappings', file_name)
            if os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    json.dump({}, f)
                logging.info(f"Soubor {file_name} byl vyčištěn.")
        
        logging.info("Reset databáze úspěšně dokončen.")
    
    except mysql.connector.Error as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při resetu databáze: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()

if __name__ == "__main__":
    reset_database()