import logging
import mysql.connector
from db_connectors import get_pg_connection, get_mysql_connection
from config import TBL_CLANEK, TBL_WP_USERS, DEFAULT_WP_USER_ID
from utils import load_mapping, save_mapping, extract_author_name

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_users():
    logging.info("Spouštím migraci uživatelů...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor(dictionary=True)
    mysql_write_cursor = mysql_conn.cursor()

    user_map = load_mapping('user_map.json')
    new_users_count = 0

    try:
        # 1. Získat unikátní autory z <PERSON>ů
        pg_cursor.execute(f"SELECT DISTINCT autor FROM {TBL_CLANEK} WHERE autor IS NOT NULL AND autor != ''")
        unique_authors_html = [row[0] for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(unique_authors_html)} unikátních autorů v článcích.")

        # 2. Extrahovat jména autorů z HTML
        unique_authors = {}  # autor_html -> autor_name
        for author_html in unique_authors_html:
            author_name = extract_author_name(author_html)
            if author_name:
                unique_authors[author_html] = author_name
            else:
                logging.warning(f"Nepodařilo se extrahovat jméno autora z: {author_html}")

        logging.info(f"Extrahováno {len(unique_authors)} jmen autorů.")

        # 3. Zkontrolovat existující uživatele ve WordPressu
        existing_users = {}  # display_name -> user_id
        mysql_cursor.execute(f"SELECT ID, user_login, display_name FROM {TBL_WP_USERS}")
        for row in mysql_cursor.fetchall():
            existing_users[row['display_name'].lower()] = row['ID']
            existing_users[row['user_login'].lower()] = row['ID']

        # 4. Migrovat nové uživatele nebo mapovat na existující
        for author_html, author_name in unique_authors.items():
            # Pokud už máme mapování pro tohoto autora, přeskočit
            if author_html in user_map:
                logging.info(f"Autor '{author_name}' již byl zpracován, přeskakuji.")
                continue

            # Zkontrolovat, zda už uživatel existuje ve WordPressu
            author_name_lower = author_name.lower()
            if author_name_lower in existing_users:
                wp_user_id = existing_users[author_name_lower]
                logging.info(f"Autor '{author_name}' již existuje ve WP s ID: {wp_user_id}. Aktualizuji mapování.")
                user_map[author_html] = wp_user_id
                continue

            # Vytvořit nového uživatele ve WordPressu
            try:
                # Generovat unikátní user_login (bez diakritiky, mezery nahradit podtržítky)
                user_login = ''.join(c for c in author_name.lower() if c.isalnum() or c.isspace()).replace(' ', '_')
                
                # Zajistit unikátnost user_login
                base_user_login = user_login
                counter = 1
                while user_login.lower() in existing_users:
                    user_login = f"{base_user_login}_{counter}"
                    counter += 1

                # Generovat heslo a email
                user_pass = f"generated_pass_{user_login}"  # V reálném nasazení použít bezpečnější heslo
                user_email = f"{user_login}@example.com"  # Placeholder email

                # Vložit do wp_users
                sql_users = f"""
                    INSERT INTO {TBL_WP_USERS}
                    (user_login, user_pass, user_nicename, user_email, user_url, user_registered, display_name)
                    VALUES (%s, MD5(%s), %s, %s, '', NOW(), %s)
                """
                mysql_write_cursor.execute(sql_users, (user_login, user_pass, user_login, user_email, author_name))
                wp_user_id = mysql_write_cursor.lastrowid
                logging.info(f"Vytvořen uživatel '{author_name}' (login: {user_login}) v {TBL_WP_USERS} s ID: {wp_user_id}")

                # Přidat do mapování
                user_map[author_html] = wp_user_id
                existing_users[user_login.lower()] = wp_user_id
                existing_users[author_name.lower()] = wp_user_id
                new_users_count += 1

                mysql_conn.commit()

            except mysql.connector.Error as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vkládání uživatele '{author_name}' do WP: {e}")
                # Fallback na výchozího uživatele
                user_map[author_html] = DEFAULT_WP_USER_ID
                logging.warning(f"Použit výchozí uživatel (ID: {DEFAULT_WP_USER_ID}) pro autora '{author_name}'.")

        # 5. Přidat mapování pro prázdné nebo chybějící autory
        user_map[''] = DEFAULT_WP_USER_ID
        user_map[None] = DEFAULT_WP_USER_ID

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_users: {e}")
    finally:
        save_mapping(user_map, 'user_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        mysql_write_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace uživatelů dokončena. Zpracováno {len(unique_authors_html)} unikátních autorů. Vytvořeno {new_users_count} nových uživatelů ve WP.")

if __name__ == "__main__":
    migrate_users()
