#!/usr/bin/env python3
import logging
import os
import sys
import json
import re
from datetime import datetime
from db_connectors import get_pg_connection, get_mysql_connection
from config import (
    TBL_CLANEK, TBL_OBRAZEK, TBL_WP_POSTS, TBL_WP_POSTMETA,
    OLD_IMAGE_BASE_PATH
)
from utils import load_mapping, save_mapping

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fix_gallery_for_article(article_id=28931, reset=False):
    """
    Speciální oprava galerie pro článek s ID 28931.
    Tato funkce řeší problém s cestami k obrázkům, které se nacházejí v podadresáři.
    """
    logging.info(f"Spouštím opravu galerie pro článek ID {article_id}...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor()
    
    # Načítání mapování
    image_map = load_mapping('image_map.json')
    article_map = load_mapping('article_map.json')
    gallery_map = load_mapping('gallery_map.json') if os.path.exists(os.path.join('mappings', 'gallery_map.json')) else {}
    
    try:
        # 1. Získat informace o článku
        pg_cursor.execute(f"SELECT id_clanek, unikatni_id, nazev FROM {TBL_CLANEK} WHERE id_clanek = %s", (article_id,))
        article = pg_cursor.fetchone()
        
        if not article:
            logging.error(f"Článek s ID {article_id} nebyl nalezen v databázi!")
            return False
        
        article_id, unique_id, article_title = article
        logging.info(f"Nalezen článek: ID={article_id}, Název={article_title}, Unikátní ID={unique_id}")
        
        # 2. Kontrola, zda článek byl migrován do WordPress
        if str(article_id) not in article_map:
            logging.error(f"Článek ID {article_id} nebyl migrován do WordPress!")
            return False
        
        wp_post_id = article_map[str(article_id)]
        logging.info(f"WordPress ID článku: {wp_post_id}")
        
        # 3. Kontrola cest k obrázkům
        article_dir = os.path.join(OLD_IMAGE_BASE_PATH, unique_id)
        if os.path.exists(article_dir) and os.path.isdir(article_dir):
            logging.info(f"Nalezen adresář pro obrázky článku: {article_dir}")
            # Vypsat obsah adresáře
            files = os.listdir(article_dir)
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
            logging.info(f"Nalezeno {len(image_files)} obrázků v adresáři článku. Například: {', '.join(image_files[:5])}")
        else:
            logging.warning(f"Adresář {article_dir} neexistuje!")
            
        # 4. Získat obrázky typu 1 z databáze (galerie)
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, popisek, priorita, typ, link 
            FROM {TBL_OBRAZEK} 
            WHERE polozka_id = %s AND active_state = 1 AND typ = 1
            ORDER BY priorita DESC, id_obrazek
        """, (unique_id,))
        gallery_items = pg_cursor.fetchall()
        
        if not gallery_items:
            logging.warning(f"Pro článek nebyly nalezeny žádné obrázky typu 1 (galerie)!")
            return False
        
        logging.info(f"Nalezeno {len(gallery_items)} obrázků pro galerii.")
        
        # 5. Kontrola, zda už existuje galerie
        gallery_key = f"article_{article_id}"
        if gallery_key in gallery_map and not reset:
            logging.warning(f"Galerie pro článek ID {article_id} již existuje.")
            logging.warning("Pro přepsání použijte parametr reset=True.")
            return False
        
        # 6. Najít ID obrázků ve WordPress
        gallery_ids = []
        
        # Procházení všech obrázků v adresáři článku
        if os.path.exists(article_dir):
            img_patterns = {}
            # Sestavit mapování vzorů názvů souborů
            for _, image_file, _, _, _, _ in gallery_items:
                base_name = os.path.splitext(os.path.basename(image_file))[0]
                img_patterns[base_name] = image_file
            
            # Procházet soubory v adresáři a mapovat
            for file_name in image_files:
                if file_name.endswith(('_100x67.jpg', '_120x80.jpg', '_68x90.jpg')):
                    continue  # Přeskočit náhledy
                
                # Hledat podle přesného názvu
                full_path = os.path.join(unique_id, file_name)
                if full_path in image_map:
                    attachment_id = image_map[full_path]['wp_id']
                    gallery_ids.append(str(attachment_id))
                    logging.info(f"Nalezeno mapování pro {full_path}")
                    continue
                
                # Hledat podle přesného názvu souboru
                if file_name in image_map:
                    attachment_id = image_map[file_name]['wp_id']
                    gallery_ids.append(str(attachment_id))
                    logging.info(f"Nalezeno mapování pro {file_name}")
                    continue
                
                # Pokud nenalezeno přímo, hledat podle vzoru názvu
                found = False
                for pattern, original_file in img_patterns.items():
                    if file_name.startswith(pattern):
                        # Zkusit najít soubor v image_map
                        for img_key in image_map:
                            if os.path.basename(img_key).startswith(pattern):
                                attachment_id = image_map[img_key]['wp_id']
                                gallery_ids.append(str(attachment_id))
                                logging.info(f"Nalezeno mapování podle vzoru: {pattern} pro {file_name}")
                                found = True
                                break
                        if found:
                            break
        
        # 7. Pokud nemáme žádné obrázky, zkusit najít podle bázového jména
        if not gallery_ids:
            for _, image_file, _, _, _, _ in gallery_items:
                base_name = os.path.splitext(os.path.basename(image_file))[0]
                found = False
                for img_key in image_map:
                    key_basename = os.path.splitext(os.path.basename(img_key))[0]
                    if key_basename.startswith(base_name) or base_name.startswith(key_basename):
                        attachment_id = image_map[img_key]['wp_id']
                        gallery_ids.append(str(attachment_id))
                        logging.info(f"Nalezeno mapování pomocí porovnání bázových jmen: {base_name} ~ {key_basename}")
                        found = True
                        break
                
                if not found:
                    # Zkusit i porovnání podle číselné části názvu (např. kuzmice-0042 vs 0042)
                    match = re.search(r'-(\d+)-', image_file)
                    if match:
                        num_part = match.group(1)
                        for img_key in image_map:
                            if num_part in img_key:
                                attachment_id = image_map[img_key]['wp_id']
                                gallery_ids.append(str(attachment_id))
                                logging.info(f"Nalezeno mapování pomocí číselné části: {num_part} v {img_key}")
                                break
        
        # 8. Speciální případ - ruční mapování známých obrázků pro ID 28931
        if article_id == 28931 and not gallery_ids:
            logging.info("Použití ručního mapování pro článek 28931")
            # Načíst existující obrázky z adresáře 654fe461eb793
            if os.path.exists(article_dir):
                existing_files = [f for f in os.listdir(article_dir) 
                                if f.endswith('.jpg') and not any(f.endswith(suffix) 
                                             for suffix in ('_100x67.jpg', '_120x80.jpg', '_68x90.jpg'))]
                
                # Sestavit mapování mezi DB záznamy a soubory
                file_mapping = {}
                for i, (img_id, img_file, _, _, _, _) in enumerate(gallery_items):
                    if i < len(existing_files):
                        real_file = existing_files[i]
                        file_mapping[img_file] = os.path.join(unique_id, real_file)
                        logging.info(f"Ruční mapování: {img_file} -> {real_file}")
                
                # Najít WP ID
                for db_file, real_path in file_mapping.items():
                    found = False
                    for img_key, img_data in image_map.items():
                        if os.path.basename(real_path) in img_key or os.path.basename(img_key) in real_path:
                            attachment_id = img_data['wp_id']
                            gallery_ids.append(str(attachment_id))
                            logging.info(f"Nalezeno pomocí ručního mapování: {db_file} -> {real_path} -> {img_key}")
                            found = True
                            break
                    if not found:
                        logging.warning(f"Nepodařilo se najít mapování pro {real_path}")
        
        # 9. Pokud stále nemáme obrázky, zkusme hledat v článku 28931
        if article_id == 28931 and not gallery_ids:
            logging.info("Hledání podle ID obrázku pro článek 28931")
            # Seznam ID obrázků z ukázky v zadání
            image_ids = [82331, 82332, 82333, 82334, 82335, 82336, 82337, 82338, 82339, 82340, 82341, 82342]
            
            for img_id in image_ids:
                # Hledat v MySQL WordPress podle meta hodnoty 'sabre_image_id'
                mysql_cursor.execute(f"""
                    SELECT post_id FROM {TBL_WP_POSTMETA} 
                    WHERE meta_key = 'sabre_image_id' AND meta_value = %s
                """, (img_id,))
                result = mysql_cursor.fetchone()
                if result:
                    attachment_id = result[0]
                    gallery_ids.append(str(attachment_id))
                    logging.info(f"Nalezeno pomocí ID obrázku: {img_id} -> {attachment_id}")
        
        if not gallery_ids:
            logging.error("Nebyly nalezeny žádné migrovatelné obrázky pro galerii!")
            return False
        
        logging.info(f"Nalezeno celkem {len(gallery_ids)} obrázků pro galerii.")
        
        # 10. Vytvořit gallery shortcode
        gallery_shortcode = f'[gallery ids="{",".join(gallery_ids)}"]'
        
        # 11. Získat stávající obsah článku
        mysql_cursor.execute(f"SELECT post_content FROM {TBL_WP_POSTS} WHERE ID = %s", (wp_post_id,))
        current_content = mysql_cursor.fetchone()[0]
        
        # 12. Kontrola, zda článek již obsahuje galerii
        gallery_pattern = r'\[gallery[^\]]*\]'
        has_gallery = re.search(gallery_pattern, current_content)
        
        if has_gallery and not reset:
            logging.warning("Článek již obsahuje galerii! Pro přepsání použijte parametr reset=True.")
            return False
        
        # 13. Přidat/aktualizovat galerii
        if has_gallery and reset:
            # Nahradit existující galerii
            new_content = re.sub(gallery_pattern, gallery_shortcode, current_content)
            logging.info("Nahrazuji existující galerii.")
        elif "<!-- gallery -->" in current_content:
            # Vložit po značce
            new_content = current_content.replace("<!-- gallery -->", f"<!-- gallery -->\n{gallery_shortcode}")
            logging.info("Vkládám galerii na místo označené <!-- gallery -->")
        else:
            # Přidat na konec
            new_content = current_content + "\n\n" + gallery_shortcode
            logging.info("Přidávám galerii na konec článku.")
        
        # 14. Aktualizovat obsah článku
        mysql_cursor.execute(f"UPDATE {TBL_WP_POSTS} SET post_content = %s WHERE ID = %s", (new_content, wp_post_id))
        
        # 15. Aktualizovat metadata
        if reset:
            # Odstranit existující metadata
            mysql_cursor.execute(f"DELETE FROM {TBL_WP_POSTMETA} WHERE post_id = %s AND meta_key = 'sabre_gallery'", (wp_post_id,))
            logging.info("Odstraněna existující metadata galerie.")
        
        # 16. Uložit nová metadata
        meta_value = {
            'original_article_id': article_id,
            'original_unique_id': unique_id,
            'article_title': article_title,
            'image_ids': gallery_ids,
            'image_count': len(gallery_ids),
            'fixed_migration': True,
            'migration_timestamp': datetime.now().isoformat()
        }
        
        mysql_cursor.execute(
            f"INSERT INTO {TBL_WP_POSTMETA} (post_id, meta_key, meta_value) VALUES (%s, %s, %s)",
            (wp_post_id, 'sabre_gallery', json.dumps(meta_value))
        )
        
        # 17. Aktualizovat mapování
        gallery_map[gallery_key] = {
            'wp_post_id': wp_post_id,
            'gallery_shortcode': gallery_shortcode,
            'image_count': len(gallery_ids),
            'fixed': True
        }
        
        save_mapping(gallery_map, 'gallery_map.json')
        
        mysql_conn.commit()
        logging.info(f"Galerie úspěšně {'aktualizována' if reset else 'vytvořena'} s {len(gallery_ids)} obrázky.")
        return True
        
    except Exception as e:
        mysql_conn.rollback()
        logging.error(f"Chyba při opravě galerie: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return False
    finally:
        pg_cursor.close()
        mysql_cursor.close()
        pg_conn.close()
        mysql_conn.close()

if __name__ == "__main__":
    reset = "--reset" in sys.argv or "-r" in sys.argv
    article_id = 28931  # Výchozí ID článku
    
    # Kontrola parametrů příkazové řádky
    for arg in sys.argv[1:]:
        if arg.isdigit():
            article_id = int(arg)
    
    if fix_gallery_for_article(article_id, reset):
        print(f"Oprava galerie pro článek ID {article_id} úspěšně dokončena.")
        sys.exit(0)
    else:
        print(f"Oprava galerie selhala. Zkontrolujte log.")
        sys.exit(1)
