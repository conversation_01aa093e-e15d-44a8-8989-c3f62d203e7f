#!/bin/bash

# Script pro spuštění propojení obrázků galérií s příspěvky
# Toto řešení <PERSON>, že galerie vytvořené během migrace budou fungovat standardně

echo "=== Nástroj pro propojení obrázků galérií s příspěvky ==="
echo

# Kontrola zda jsme ve správném adresáři
if [ ! -f "attach_gallery_images.py" ]; then
    echo "CHYBA: Script musí být spuštěn z adresáře, kde se nachází attach_gallery_images.py"
    exit 1
fi

# Výchozí hodnoty
DRY_RUN=false

# Zpracování argumentů
while [[ $# -gt 0 ]]; do
    case "$1" in
        --dry-run|-d)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            echo "Použití: $0 [OPTIONS]"
            echo
            echo "Propojí obrázky galérií s příspěvky ve WordPress, aby fungovaly standardním způsobem."
            echo "Toto je jednor<PERSON>zov<PERSON> skript, kter<PERSON> se spouští po migraci galérií."
            echo
            echo "Možnosti:"
            echo "  -d, --dry-run    Simulační režim - neprovedou se skutečné změny v databázi"
            echo "  -h, --help       Zobrazit tuto nápovědu"
            echo
            exit 0
            ;;
        *)
            echo "Neznámý parametr: $1"
            echo "Použijte '$0 --help' pro nápovědu."
            shift
            ;;
    esac
done

# Sestavení příkazu
CMD="./attach_gallery_images.py"

if [ "$DRY_RUN" = true ]; then
    CMD="$CMD --dry-run"
    echo "Režim: SIMULACE (změny nebudou zapsány)"
else
    echo "Režim: OSTRÝ běh (změny budou zapsány do databáze)"
fi

echo
echo "Tento skript propojí obrázky galérií s příspěvky ve WordPress nastavením pole post_parent."
echo "Tím zajistí, že galerie vytvořené během migrace budou správně zobrazovány."
echo
echo "DŮLEŽITÉ: Před spuštěním v ostrém režimu doporučujeme nejprve spustit v simulačním režimu."
echo "Simulační režim: $0 --dry-run"
echo

# Potvrzení v ostrém režimu
if [ "$DRY_RUN" = false ]; then
    read -p "Spustit propojení obrázků? (y/n): " confirm
    if [[ $confirm != [yY] ]]; then
        echo "Operace zrušena."
        exit 0
    fi
fi

echo "Spouštím: $CMD"
echo "============================================="

# Spuštění příkazu
$CMD

STATUS=$?
if [ $STATUS -eq 0 ]; then
    echo "============================================="
    echo "Propojení obrázků galérií s příspěvky úspěšně dokončeno."
else
    echo "============================================="
    echo "CHYBA: Propojení obrázků skončilo s kódem $STATUS."
fi

echo
echo "Pro zobrazení detailního výstupu zkontrolujte log soubor v adresáři logs/"
