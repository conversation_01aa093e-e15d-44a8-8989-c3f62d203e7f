#!/usr/bin/env python3
import os
import re
import json
import logging
import argparse
import mysql.connector
import psycopg2
from datetime import datetime
from difflib import SequenceMatcher

# Nastavení logování
LOG_FILE = os.path.join('logs', f'fix_content_image_paths_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
os.makedirs('logs', exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_pg_connection():
    """Vytvoří a vrátí spojení s PostgreSQL databází pro SABRE"""
    pg_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('PG_'):
                    pg_config[key[3:].lower()] = value

    return psycopg2.connect(
        host=pg_config.get('host', 'localhost'),
        user=pg_config.get('user', ''),
        password=pg_config.get('password', ''),
        database=pg_config.get('dbname', ''),
        port=int(pg_config.get('port', 5432))
    )

def get_mysql_connection():
    """Vytvoří a vrátí spojení s MySQL databází pro WordPress"""
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    sabre_prefix = 'prefix_'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
                elif line.startswith('SABRE_TABLE_PREFIX='):
                    sabre_prefix = line.split('=', 1)[1]
    
    return wp_prefix, sabre_prefix

def get_image_base_path():
    """Získá základní cestu k obrázkům."""
    path = '/home/<USER>/sabre/public_html_backup/public_html/obrazek'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('OLD_IMAGE_BASE_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def get_wp_uploads_path():
    """Získá cestu k WordPress uploads adresáři."""
    path = '/home/<USER>/www/dumabyt/wp-content/uploads'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_UPLOADS_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def get_wp_site_url():
    """Získá URL WordPress webu."""
    url = 'http://dumabyt.test'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_SITE_URL='):
                    url = line.split('=', 1)[1].rstrip('/')
    
    return url

def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def save_mapping(data, filename):
    """Uloží mapování do souboru."""
    try:
        os.makedirs('mappings', exist_ok=True)
        filepath = os.path.join('mappings', filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logging.error(f"Chyba při ukládání mapování {filename}: {e}")
        return False

def normalize_path(path):
    """Normalizuje cestu k souboru pro konzistentní porovnávání."""
    # Odstranit query parametry a fragmenty
    path = path.split('?')[0].split('#')[0]
    
    # Zajistit, že všechny oddělovače jsou '/'
    path = path.replace('\\', '/')
    
    # Odstranit opakující se '/'
    while '//' in path:
        path = path.replace('//', '/')
    
    # Odstranit trailing '/'
    path = path.rstrip('/')
    
    # Získat jen název souboru, pokud potřebujeme
    filename = os.path.basename(path)
    
    return path, filename

def similar(a, b):
    """Vrátí míru podobnosti mezi dvěma řetězci (0-1)."""
    return SequenceMatcher(None, a, b).ratio()

def extract_image_paths(content):
    """
    Extrahuje všechny cesty k obrázkům z HTML obsahu.
    Používá rozšířené regex patterny pro zachycení všech variant.
    """
    paths = []
    
    # 1. Standardní img tag
    standard_img_pattern = re.compile(r'<img[^>]*?src=["\']([^"\']+?)["\'][^>]*?>')
    paths.extend(standard_img_pattern.findall(content))
    
    # 2. Img tag s nestandardními mezerami nebo zalomením řádků
    nonstandard_img_pattern = re.compile(r'<\s*img\s+[^>]*?src\s*=\s*["\']([^"\']+?)["\'][^>]*?>')
    paths.extend(nonstandard_img_pattern.findall(content))
    
    # 3. Data-src atribut (používaný pro lazy loading)
    data_src_pattern = re.compile(r'<[^>]*?data-src=["\']([^"\']+?)["\'][^>]*?>')
    paths.extend(data_src_pattern.findall(content))
    
    # 4. Background image v CSS
    bg_image_pattern = re.compile(r'background(-image)?:\s*url\(["\']?([^"\'\)]+)["\']?\)')
    for match in bg_image_pattern.finditer(content):
        paths.append(match.group(2))
    
    # 5. Inline style s background-image
    inline_bg_pattern = re.compile(r'style=["\'].*?background(-image)?:\s*url\(["\']?([^"\'\)]+)["\']?\).*?["\']')
    for match in inline_bg_pattern.finditer(content):
        paths.append(match.group(2))
    
    # Odstranit duplicity a vrátit unikátní cesty
    return list(set(paths))

def find_matching_wp_image(filename, mysql_cursor, wp_prefix, image_map):
    """
    Hledá odpovídající WordPress obrázek podle názvu souboru.
    Používá fuzzy matching pro případy, kdy se názvy mírně liší.
    """
    # 1. Nejprve zkusit přesnou shodu v mapování
    for old_path, data in image_map.items():
        if filename in old_path and 'wp_url' in data and data['wp_url']:
            return data['wp_url']
    
    # 2. Zkusit přesnou shodu podle názvu souboru ve WordPress
    base_name = os.path.splitext(filename)[0]
    mysql_cursor.execute(f"""
        SELECT ID, guid
        FROM {wp_prefix}posts
        WHERE post_type = 'attachment'
        AND guid LIKE %s
    """, (f"%{filename}%",))
    
    exact_matches = mysql_cursor.fetchall()
    if exact_matches:
        return exact_matches[0]['guid']
    
    # 3. Zkusit fuzzy matching v databázi
    mysql_cursor.execute(f"""
        SELECT ID, guid, post_title
        FROM {wp_prefix}posts
        WHERE post_type = 'attachment'
    """)
    
    all_attachments = mysql_cursor.fetchall()
    best_match = None
    best_score = 0.7  # Minimální skóre pro shodu
    
    for attachment in all_attachments:
        attach_filename = os.path.basename(attachment['guid'])
        score = similar(filename, attach_filename)
        
        if score > best_score:
            best_score = score
            best_match = attachment['guid']
    
    return best_match

def replace_image_paths_in_content(content, mysql_cursor, wp_prefix, image_map, verbose=False):
    """
    Nahradí všechny cesty k obrázkům v HTML obsahu.
    """
    # Extrahovat všechny cesty k obrázkům
    image_paths = extract_image_paths(content)
    if not image_paths:
        return content, 0
    
    if verbose:
        logging.info(f"Nalezeno {len(image_paths)} cest k obrázkům v obsahu.")
    
    # Mapování starých cest na nové
    replacement_map = {}
    
    # Pro každou cestu najít odpovídající WordPress URL
    for old_path in image_paths:
        # Přeskočit URLs, které již jsou na WordPress doméně
        wp_site_url = get_wp_site_url()
        if wp_site_url in old_path and '/wp-content/uploads/' in old_path:
            if verbose:
                logging.info(f"Cesta {old_path} již směřuje na WordPress, přeskakuji.")
            continue
        
        # Normalizovat cestu
        normalized_path, filename = normalize_path(old_path)
        
        # Ignorovat prázdné nebo nevhodné cesty
        if not filename or not any(filename.lower().endswith(ext) for ext in ('.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp')):
            if verbose:
                logging.info(f"Cesta {old_path} není podporovaný formát obrázku, přeskakuji.")
            continue
        
        # Najít odpovídající WordPress URL
        wp_url = find_matching_wp_image(filename, mysql_cursor, wp_prefix, image_map)
        
        if wp_url:
            replacement_map[old_path] = wp_url
            if verbose:
                logging.info(f"Nahrazuji: {old_path} -> {wp_url}")
        else:
            if verbose:
                logging.warning(f"Pro cestu {old_path} nebyl nalezen odpovídající WordPress obrázek.")
    
    # Provést nahrazení v obsahu
    new_content = content
    replaced_count = 0
    
    for old_path, new_path in replacement_map.items():
        # Escapovat speciální znaky v cestě pro regex
        old_path_escaped = re.escape(old_path)
        
        # Nahradit všechny výskyty v různých kontextech
        patterns = [
            (f'src=["\']({old_path_escaped})["\']', f'src="{new_path}"'),
            (f'src=\\s*["\']({old_path_escaped})["\']', f'src="{new_path}"'),
            (f'data-src=["\']({old_path_escaped})["\']', f'data-src="{new_path}"'),
            (f'url\\(["\']?({old_path_escaped})["\']?\\)', f'url("{new_path}")'),
        ]
        
        for pattern, replacement in patterns:
            content_before = new_content
            new_content = re.sub(pattern, replacement, new_content)
            if content_before != new_content:
                replaced_count += 1
    
    return new_content, replaced_count

def fix_content_image_paths_for_article(wp_article_id, dry_run=False, verbose=False):
    """
    Opraví cesty k obrázkům v obsahu jednoho článku.
    
    Args:
        wp_article_id: ID článku ve WordPress
        dry_run: Pokud True, neprovádí skutečné změny
        verbose: Pokud True, vypisuje podrobnější informace
    """
    try:
        if verbose:
            logging.info(f"Opravuji cesty k obrázkům v článku WP ID: {wp_article_id}")
        
        # Připojení k databázi
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, _ = get_table_prefixes()
        
        # Načtení mapování obrázků
        image_map = load_mapping('image_map.json')
        if not image_map:
            logging.warning("Chybí mapování obrázků, oprava nebude tak účinná.")
            image_map = {}
        elif verbose:
            logging.info(f"Načteno {len(image_map)} záznamů z image_map.json.")
        
        # Získat obsah článku
        mysql_cursor.execute(f"""
            SELECT ID, post_title, post_content
            FROM {wp_prefix}posts
            WHERE ID = %s AND post_type = 'post'
        """, (wp_article_id,))
        
        article = mysql_cursor.fetchone()
        if not article:
            logging.warning(f"Článek s ID {wp_article_id} nebyl nalezen ve WordPress.")
            return False
        
        title = article['post_title']
        content = article['post_content']
        
        # Opravit cesty k obrázkům v obsahu
        new_content, replaced_count = replace_image_paths_in_content(
            content, mysql_cursor, wp_prefix, image_map, verbose
        )
        
        # Pokud se obsah nezměnil, není třeba aktualizovat
        if content == new_content:
            if verbose:
                logging.info(f"Článek {wp_article_id} ({title}): Žádné změny.")
            return False
        
        # Aktualizovat obsah v databázi
        if not dry_run:
            mysql_cursor.execute(f"""
                UPDATE {wp_prefix}posts
                SET post_content = %s
                WHERE ID = %s
            """, (new_content, wp_article_id))
            mysql_conn.commit()
            logging.info(f"Článek {wp_article_id} ({title}): Nahrazeno {replaced_count} cest k obrázkům.")
        else:
            logging.info(f"[DRY RUN] Článek {wp_article_id} ({title}): Nahrazeno by bylo {replaced_count} cest k obrázkům.")
        
        return True
        
    except Exception as e:
        logging.error(f"Chyba při opravě cest v článku {wp_article_id}: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return False
    finally:
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

def fix_content_image_paths_for_all_articles(limit=None, start_from=0, dry_run=False, verbose=False):
    """
    Opraví cesty k obrázkům ve všech článcích.
    
    Args:
        limit: Maximální počet článků ke zpracování
        start_from: Index, od kterého začít
        dry_run: Pokud True, neprovádí skutečné změny
        verbose: Pokud True, vypisuje podrobnější informace
    """
    try:
        start_time = datetime.now()
        logging.info(f"Spouštím opravu cest k obrázkům ve všech článcích (limit={limit}, start_from={start_from}, dry_run={dry_run})...")
        
        # Připojení k databázi
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, _ = get_table_prefixes()
        
        # Získat všechny články
        mysql_cursor.execute(f"""
            SELECT ID, post_title, post_status
            FROM {wp_prefix}posts
            WHERE post_type = 'post' AND post_status IN ('publish', 'draft')
            ORDER BY ID DESC
            LIMIT %s OFFSET %s
        """, (limit if limit else 9999999, start_from))
        
        articles = mysql_cursor.fetchall()
        total_articles = len(articles)
        logging.info(f"Nalezeno {total_articles} článků.")
        
        # Statistiky
        fixed_count = 0
        skipped_count = 0
        error_count = 0
        
        # Opravit cesty v každém článku
        for i, article in enumerate(articles, 1):
            wp_id = article['ID']
            title = article['post_title']
            
            try:
                if verbose:
                    logging.info(f"Zpracovávám článek {i}/{total_articles}: {wp_id} - {title}")
                
                result = fix_content_image_paths_for_article(wp_id, dry_run, verbose)
                
                if result:
                    fixed_count += 1
                else:
                    skipped_count += 1
                    
            except Exception as e:
                logging.error(f"Chyba při zpracování článku {wp_id} ({title}): {e}")
                error_count += 1
        
        # Výsledné statistiky
        end_time = datetime.now()
        duration = end_time - start_time
        
        logging.info("-" * 50)
        logging.info("SOUHRN OPRAVY CEST K OBRÁZKŮM:")
        logging.info(f"Celkem zpracováno: {total_articles}")
        logging.info(f"Opraveno: {fixed_count}")
        logging.info(f"Přeskočeno: {skipped_count}")
        logging.info(f"Chyby: {error_count}")
        logging.info(f"Doba trvání: {duration}")
        
        return fixed_count
            
    except Exception as e:
        logging.error(f"Obecná chyba: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return 0
    finally:
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

def main():
    parser = argparse.ArgumentParser(description='Oprava cest k obrázkům v obsahu článků')
    parser.add_argument('--wp-id', type=int, help='ID konkrétního WordPress článku k opravě')
    parser.add_argument('--all', action='store_true', help='Opravit všechny články')
    parser.add_argument('--limit', type=int, help='Maximální počet článků ke zpracování')
    parser.add_argument('--start-from', type=int, default=0, help='Index, od kterého začít')
    parser.add_argument('--dry-run', action='store_true', help='Pouze simulovat opravy bez ukládání')
    parser.add_argument('--verbose', action='store_true', help='Výpis podrobnějších informací')
    
    args = parser.parse_args()
    
    if args.wp_id:
        # Opravit konkrétní článek
        fix_content_image_paths_for_article(args.wp_id, args.dry_run, args.verbose)
    elif args.all:
        # Opravit všechny články
        fix_content_image_paths_for_all_articles(args.limit, args.start_from, args.dry_run, args.verbose)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
