#!/bin/bash

# Script pro vynucenou migraci galerií
# Podporuje jak migraci pro konk<PERSON><PERSON><PERSON><PERSON>, tak migraci samostatných galerií

echo "=== Nástroj pro vynucenou migraci galerií ==="
echo

# Kontrola zda jsme ve správném adresáři
if [ ! -f "migrate_galleries.py" ]; then
    echo "CHYBA: Script musí být spuštěn z adresáře, kde se nachází migrate_galleries.py"
    exit 1
fi

# Výchozí hodnoty
ARTICLE_ID=""
FORCE_MODE=true
RESET_MODE=false
STANDALONE_MODE=false
LIST_GALLERIES=false

# Nápověda
function show_help {
    echo "Použití: $0 [OPTIONS] [ID_ČLÁNKU]"
    echo
    echo "Nástroj pro vynucenou migraci galerií z SABRE do WordPress"
    echo
    echo "Možnosti:"
    echo "  --id=NUMBER       ID článku pro migraci galerie"
    echo "  -r, --reset       Přepsat existující galerii, pokud existuje"
    echo "  -s, --standalone  Migrovat samostatné galerie (z adresářů galerie*)"
    echo "  -l, --list        Vypsat seznam dostupných samostatných galerií"
    echo "  -h, --help        Zobrazit tuto nápovědu"
    echo
    echo "Příklady:"
    echo "  $0 28931          Migrovat galerii pro článek ID 28931"
    echo "  $0 28931 --reset  Migrovat galerii pro článek ID 28931 a přepsat existující"
    echo "  $0 --standalone   Migrovat všechny samostatné galerie"
    echo
    exit 0
}

# Zpracování argumentů
while [[ $# -gt 0 ]]; do
    case "$1" in
        --id=*)
            ARTICLE_ID="${1#*=}"
            shift
            ;;
        --reset|-r)
            RESET_MODE=true
            shift
            ;;
        --standalone|-s)
            STANDALONE_MODE=true
            shift
            ;;
        --list|-l)
            LIST_GALLERIES=true
            shift
            ;;
        --help|-h)
            show_help
            ;;
        *)
            # Pokud není rozpoznán formát --parametr=hodnota, zkusíme, jestli to není přímo ID
            if [[ "$1" =~ ^[0-9]+$ ]]; then
                ARTICLE_ID="$1"
            else
                echo "Neznámý parametr: $1"
                echo "Použijte '$0 --help' pro nápovědu."
            fi
            shift
            ;;
    esac
done

# Sestavení příkazu
if [ "$LIST_GALLERIES" = true ]; then
    CMD="python migrate_galleries.py --list-galleries"
    echo "Režim: Výpis dostupných samostatných galerií"
elif [ "$STANDALONE_MODE" = true ]; then
    CMD="python migrate_galleries.py --standalone"
    
    if [ "$FORCE_MODE" = true ]; then
        CMD="$CMD --force"
    fi
    
    if [ "$RESET_MODE" = true ]; then
        CMD="$CMD --reset"
    fi
    
    echo "Režim: Migrace samostatných galerií"
    if [ "$RESET_MODE" = true ]; then
        echo " - s přepsáním existujících galerií"
    fi
else
    # Kontrola ID článku
    if [ -z "$ARTICLE_ID" ]; then
        # Pokud není zadáno ID, použijeme výchozí příklad
        ARTICLE_ID="28931"
        echo "Nebyl zadán článek, používám výchozí ukázkový článek ID: 28931"
    else
        echo "Zpracovávám článek s ID: $ARTICLE_ID"
    fi

    CMD="python migrate_galleries.py $ARTICLE_ID --force"
    if [ "$RESET_MODE" = true ]; then
        CMD="$CMD --reset"
        echo "Režim: Vynucená migrace článkové galerie s resetem existující galerie"
    else
        echo "Režim: Vynucená migrace článkové galerie (bez resetu)"
    fi
fi

echo
echo "Spouštím: $CMD"
echo "============================================="

# Spuštění příkazu
$CMD

STATUS=$?
if [ $STATUS -eq 0 ]; then
    echo "============================================="
    echo "Migrace galerie pro článek ID $ARTICLE_ID dokončena úspěšně."
else
    echo "============================================="
    echo "CHYBA: Migrace galerie selhala s kódem $STATUS."
    echo "Zkontrolujte log soubory v adresáři logs/ pro více informací."
fi
