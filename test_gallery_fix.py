#!/usr/bin/env python3
import os
import json
import mysql.connector
import psycopg2
import sys

# Přímá implementace namísto importu z utils
def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def get_pg_connection():
    """Vytvoří a vrátí spojení s PostgreSQL databází pro SABRE"""
    pg_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('PG_'):
                    pg_config[key[3:].lower()] = value

    return psycopg2.connect(
        host=pg_config.get('host', 'localhost'),
        user=pg_config.get('user', ''),
        password=pg_config.get('password', ''),
        database=pg_config.get('dbname', ''),
        port=int(pg_config.get('port', 5432))
    )

def get_mysql_connection():
    """Vytvoří a vrátí spojení s MySQL databází pro WordPress"""
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    sabre_prefix = 'prefix_'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
                elif line.startswith('SABRE_TABLE_PREFIX='):
                    sabre_prefix = line.split('=', 1)[1]
    
    return wp_prefix, sabre_prefix

def get_image_base_path():
    """Získá základní cestu k obrázkům."""
    path = '/home/<USER>/sabre/public_html_backup/public_html/obrazek'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('OLD_IMAGE_BASE_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def test_gallery_fix(article_id=29152):
    try:
        print(f"--- ANALÝZA GALERIE PRO ČLÁNEK {article_id} ---")
        
        # Připojení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        image_map = load_mapping('image_map.json')
        article_map = load_mapping('article_map.json')
        if not image_map:
            print("Chybí mapování obrázků.")
            return
        
        print(f"Načteno {len(image_map)} záznamů z image_map.json.")
        print(f"Načteno {len(article_map)} záznamů z article_map.json.")
        
        # Získat informace o článku
        pg_cursor.execute(f"""
            SELECT id_clanek, unikatni_id, nazev 
            FROM {sabre_prefix}clanek 
            WHERE id_clanek = %s
        """, (article_id,))
        
        article_info = pg_cursor.fetchone()
        if not article_info:
            print(f"Článek s ID {article_id} nebyl nalezen v PostgreSQL.")
            return
        
        _, unique_id, article_title = article_info
        print(f"Článek ID {article_id}: {article_title} (unique_id: {unique_id})")
        
        # Získat obrázky článku z PostgreSQL
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, priorita, active_state
            FROM {sabre_prefix}obrazek 
            WHERE polozka_id = %s AND active_state = 1 AND typ = 1
            ORDER BY priorita DESC, id_obrazek
        """, (unique_id,))
        
        obrazky = pg_cursor.fetchall()
        print(f"\nNalezeno {len(obrazky)} obrázků v PostgreSQL:")
        for i, (img_id, img_file, img_prio, img_active) in enumerate(obrazky, 1):
            print(f"{i}. ID={img_id}, Soubor={img_file}, Priorita={img_prio}, Aktivní={img_active}")
        
        # 1. Zkusit najít článek v article_map
        wp_article_id = None
        if str(article_id) in article_map:
            wp_article_id = article_map[str(article_id)]
            print(f"\nNalezeno v article_map: Článek ID {article_id} -> WordPress ID {wp_article_id}")
        
        # 2. Pokud nebylo nalezeno v article_map, zkusit podle sabre_original_id
        if not wp_article_id:
            mysql_cursor.execute(f"""
                SELECT p.ID, p.post_title
                FROM {wp_prefix}posts p
                JOIN {wp_prefix}postmeta pm ON p.ID = pm.post_id
                WHERE pm.meta_key = 'sabre_original_id' AND pm.meta_value = %s
            """, (article_id,))
            
            wp_article = mysql_cursor.fetchone()
            if wp_article:
                wp_article_id = wp_article['ID']
                print(f"\nNalezeno podle sabre_original_id: WordPress ID {wp_article_id}, Titulek={wp_article['post_title']}")
        
        # 3. Pokud stále nic, zkusit podle názvu
        if not wp_article_id:
            mysql_cursor.execute(f"""
                SELECT ID, post_title
                FROM {wp_prefix}posts
                WHERE post_type = 'post'
                AND post_title LIKE %s
            """, (f'%{article_title}%',))
            
            wp_articles = mysql_cursor.fetchall()
            if wp_articles:
                print(f"\nNalezeno {len(wp_articles)} článků podle názvu:")
                for wp_article in wp_articles:
                    print(f"WordPress ID: {wp_article['ID']}, Titulek: {wp_article['post_title']}")
                    
                    # Zkontrolovat metadata
                    mysql_cursor.execute(f"""
                        SELECT meta_key, meta_value
                        FROM {wp_prefix}postmeta
                        WHERE post_id = %s AND meta_key IN ('sabre_original_id', 'sabre_gallery')
                    """, (wp_article['ID'],))
                    
                    metadata = mysql_cursor.fetchall()
                    if metadata:
                        print("  Metadata:")
                        for meta in metadata:
                            print(f"  - {meta['meta_key']}: {meta['meta_value'][:50]}..." if len(meta['meta_value']) > 50 else meta['meta_value'])
                    
                    # Použijeme první nalezený článek jako wp_article_id
                    if not wp_article_id:
                        wp_article_id = wp_article['ID']
                        print(f"\nPoužijeme WordPress ID {wp_article_id} pro další analýzu.")
        
        if not wp_article_id:
            print(f"\nČlánek s ID {article_id} nebyl nalezen ve WordPress.")
            return

        # Získat metadata galerie
        mysql_cursor.execute(f"""
            SELECT meta_id, meta_value
            FROM {wp_prefix}postmeta
            WHERE post_id = %s AND meta_key = 'sabre_gallery'
        """, (wp_article_id,))
        
        gallery_meta = mysql_cursor.fetchone()
        if not gallery_meta:
            print("\nČlánek nemá žádná metadata sabre_gallery.")
            return
        
        gallery_data = json.loads(gallery_meta['meta_value'])
        print(f"\nMetadata galerie:")
        print(f"Počet obrázků v metadatech: {gallery_data.get('image_count', 0)}")
        print(f"Image IDs v metadatech: {gallery_data.get('image_ids', [])}")
        
        # Zkontrolovat fyzické soubory
        image_base_path = get_image_base_path()
        article_image_dir = os.path.join(image_base_path, unique_id)
        
        if os.path.exists(article_image_dir):
            files = os.listdir(article_image_dir)
            image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) 
                          and not f.endswith(('_100x67.jpg', '_120x80.jpg', '_68x90.jpg', '_100x66.jpg', '_100x57.jpg', '_635x363.jpg', '_639x365.jpg', '_700x400.jpg', '_303x173.jpg'))]
            
            print(f"\nNalezeno {len(image_files)} fyzických obrázkových souborů (bez náhledů):")
            for i, img_file in enumerate(image_files, 1):
                full_path = os.path.join(article_image_dir, img_file)
                file_size = os.path.getsize(full_path) if os.path.isfile(full_path) else 0
                print(f"{i}. {img_file} (velikost: {file_size} bajtů)")
            
            # Nyní zkusíme přiřadit fyzické soubory k záznamům v PostgreSQL
            print("\nMapování PostgreSQL souborů k fyzickým souborům:")
            pg_to_physical = {}
            
            for img_id, img_file, _, _ in obrazky:
                base_name = os.path.splitext(img_file)[0]
                matched_file = None
                
                for phys_file in image_files:
                    phys_base = os.path.splitext(phys_file)[0]
                    if phys_base.startswith(base_name) or base_name in phys_base:
                        matched_file = phys_file
                        break
                
                pg_to_physical[img_file] = matched_file
                print(f"- PostgreSQL: {img_file} -> Fyzický soubor: {matched_file}")
            
            # Kontrola mapování v image_map.json
            print("\nMapování v image_map.json:")
            for pg_file, physical_file in pg_to_physical.items():
                if physical_file:
                    key_with_unique_id = f"{unique_id}/{physical_file}"
                    
                    if key_with_unique_id in image_map:
                        wp_id = image_map[key_with_unique_id].get('wp_id')
                        print(f"- {pg_file} -> {physical_file} -> WordPress ID: {wp_id}")
                    else:
                        print(f"- {pg_file} -> {physical_file} -> CHYBÍ MAPOVÁNÍ")
                else:
                    print(f"- {pg_file} -> NENALEZEN FYZICKÝ SOUBOR")
            
            # Najít obrázky ve WordPress podle názvu nebo části názvu
            print("\nHledání obrázků ve WordPress podle názvu:")
            for pg_file, physical_file in pg_to_physical.items():
                if physical_file:
                    # Zkusit najít přímo podle názvu
                    mysql_cursor.execute(f"""
                        SELECT ID, post_title, guid
                        FROM {wp_prefix}posts
                        WHERE post_type = 'attachment'
                        AND post_title LIKE %s
                    """, (f"%{os.path.splitext(physical_file)[0]}%",))
                    
                    wp_images = mysql_cursor.fetchall()
                    if wp_images:
                        print(f"- Pro {pg_file} -> {physical_file} nalezeno {len(wp_images)} obrázků v WordPress:")
                        for img in wp_images:
                            print(f"  * ID: {img['ID']}, Titulek: {img['post_title']}, URL: {img['guid']}")
                    else:
                        print(f"- Pro {pg_file} -> {physical_file} nebyly nalezeny žádné obrázky v WordPress")
        else:
            print(f"Adresář s obrázky {article_image_dir} neexistuje.")
    
    except Exception as e:
        print(f"Chyba: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Zavřít spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        article_id = int(sys.argv[1])
        test_gallery_fix(article_id)
    else:
        test_gallery_fix()
