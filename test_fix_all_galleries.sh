#!/bin/bash

# Tento skript testuje opravu galerií v režimu dry-run na omezeném počtu článků

# Změnit do adresáře migration
cd "$(dirname "$0")"

# Parametry pro test
LIMIT=5  # Testuje pouze 5 článků
VERBOSE=true  # Zapíná podrobné logování

# Spustit skript v režimu dry-run
echo "Spouštím test opravy galerií (dry-run, limit 5 článků)..."
echo ""

./fix_all_galleries_systematic.sh --dry-run --limit $LIMIT --verbose

echo ""
echo "Test dokončen. Zkontrolujte výsledky v logu výše."
echo "Pro spuštění skutečné opravy všech galerií použijte: ./fix_all_galleries_systematic.sh"
