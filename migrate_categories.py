import logging
import mysql.connector
from db_connectors import get_pg_connection, get_mysql_connection
from config import TBL_CLANEK, TBL_RUBRIKA, TBL_WP_TERMS, TBL_WP_TERM_TAXONOMY
from utils import load_mapping, save_mapping, generate_slug

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def migrate_categories():
    logging.info("Spouštím migraci kategorií...")
    pg_conn = get_pg_connection()
    mysql_conn = get_mysql_connection()
    pg_cursor = pg_conn.cursor()
    mysql_cursor = mysql_conn.cursor(dictionary=True)  # Vrací výsledky jako slovníky
    mysql_write_cursor = mysql_conn.cursor()

    category_map = load_mapping('category_map.json')
    new_categories_count = 0
    processed_sabre_ids = set(map(int, category_map.keys()))  # Urychlení kontroly

    try:
        # 1. Získat unikátní rubrika_id z článků
        pg_cursor.execute(f"SELECT DISTINCT rubrika_id FROM {TBL_CLANEK} WHERE rubrika_id IS NOT NULL AND rubrika_id > 0")
        unique_rubrika_ids = [row[0] for row in pg_cursor.fetchall()]
        logging.info(f"Nalezeno {len(unique_rubrika_ids)} unikátních ID rubrik v článcích.")

        # 2. Získat názvy rubrik ze SABRE DB
        sabre_categories = {}
        if unique_rubrika_ids:
            # Dávkové načtení pro efektivitu
            placeholders = ', '.join(['%s'] * len(unique_rubrika_ids))
            fetch_categories_sql = f"SELECT id_rubrika, nazev FROM {TBL_RUBRIKA} WHERE id_rubrika IN ({placeholders})"
            try:
                pg_cursor.execute(fetch_categories_sql, unique_rubrika_ids)
                for row in pg_cursor.fetchall():
                    sabre_categories[row[0]] = row[1]  # Mapování sabre_id -> nazev
            except Exception as e:
                logging.error(f"Chyba při načítání názvů rubrik z {TBL_RUBRIKA}: {e}")
                logging.warning("Migrace kategorií bude pokračovat bez názvů (nebo použije pouze ID).")
                # Alternativně: Generovat názvy zde, pokud tabulka neexistuje
                for rid in unique_rubrika_ids:
                    sabre_categories[rid] = f"Rubrika {rid}"

        # 3. Zkontrolovat existující termy ve WordPressu
        existing_terms = {}
        mysql_cursor.execute(f"""
            SELECT t.term_id, t.name, t.slug
            FROM {TBL_WP_TERMS} t
            JOIN {TBL_WP_TERM_TAXONOMY} tt ON t.term_id = tt.term_id
            WHERE tt.taxonomy = 'category'
        """)
        for row in mysql_cursor.fetchall():
            existing_terms[row['slug']] = row['term_id']
            existing_terms[row['name']] = row['term_id']  # Pro kontrolu dle jména

        # 4. Migrovat nové kategorie
        for sabre_id in unique_rubrika_ids:
            if sabre_id in processed_sabre_ids:
                logging.info(f"Rubrika ID {sabre_id} již byla zpracována, přeskakuji.")
                continue

            sabre_name = sabre_categories.get(sabre_id)
            if not sabre_name:
                sabre_name = f"Neznámá Rubrika {sabre_id}"  # Fallback název
                logging.warning(f"Název pro rubriku ID {sabre_id} nebyl nalezen, používám '{sabre_name}'.")

            slug = generate_slug(sabre_name)

            # Zkontrolovat, zda už termín (dle slugu nebo jména) neexistuje
            if slug in existing_terms:
                wp_term_id = existing_terms[slug]
                logging.info(f"Kategorie '{sabre_name}' (slug: {slug}) již existuje ve WP s ID: {wp_term_id}. Aktualizuji mapování.")
                category_map[str(sabre_id)] = wp_term_id
                processed_sabre_ids.add(sabre_id)
                continue
            if sabre_name in existing_terms:
                wp_term_id = existing_terms[sabre_name]
                logging.info(f"Kategorie '{sabre_name}' již existuje ve WP s ID: {wp_term_id}. Aktualizuji mapování.")
                category_map[str(sabre_id)] = wp_term_id
                processed_sabre_ids.add(sabre_id)
                continue

            try:
                # Vložit do wp_terms
                sql_terms = f"INSERT INTO {TBL_WP_TERMS} (name, slug, term_group) VALUES (%s, %s, 0)"
                mysql_write_cursor.execute(sql_terms, (sabre_name, slug))
                wp_term_id = mysql_write_cursor.lastrowid
                logging.info(f"Vytvořena kategorie '{sabre_name}' (slug: {slug}) v {TBL_WP_TERMS} s ID: {wp_term_id}")

                # Vložit do wp_term_taxonomy
                sql_taxonomy = f"""
                    INSERT INTO {TBL_WP_TERM_TAXONOMY}
                    (term_id, taxonomy, description, parent, count)
                    VALUES (%s, 'category', '', 0, 0)
                """
                mysql_write_cursor.execute(sql_taxonomy, (wp_term_id,))
                logging.info(f"Vytvořen záznam v {TBL_WP_TERM_TAXONOMY} pro term_id {wp_term_id}")

                mysql_conn.commit()
                category_map[str(sabre_id)] = wp_term_id
                processed_sabre_ids.add(sabre_id)
                new_categories_count += 1

            except mysql.connector.Error as e:
                mysql_conn.rollback()
                logging.error(f"Chyba při vkládání kategorie ID {sabre_id} ('{sabre_name}') do WP: {e}")

    except Exception as e:
        logging.error(f"Obecná chyba v migrate_categories: {e}")
    finally:
        save_mapping(category_map, 'category_map.json')
        pg_cursor.close()
        mysql_cursor.close()
        mysql_write_cursor.close()
        pg_conn.close()
        mysql_conn.close()
        logging.info(f"Migrace kategorií dokončena. Zpracováno {len(unique_rubrika_ids)} unikátních ID. Vytvořeno {new_categories_count} nových kategorií ve WP.")

if __name__ == "__main__":
    migrate_categories()
