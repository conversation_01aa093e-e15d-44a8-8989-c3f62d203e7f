#!/usr/bin/env python3
import os
import re
import json
import mysql.connector
import psycopg2
from datetime import datetime
from fix_gallery_mapping_add_images import (
    get_pg_connection, get_mysql_connection, get_table_prefixes,
    get_image_base_path, get_wp_uploads_path, get_wp_site_url,
    load_mapping, save_mapping, migrate_image_to_wordpress,
    get_default_wp_user_id
)

def fix_post_content_images_for_article(wp_article_id, pg_article_id, dry_run=False):
    """
    Opraví obrázky vložené přímo v obsahu článku.
    
    Args:
        wp_article_id: ID článku ve WordPress
        pg_article_id: ID článku v PostgreSQL
        dry_run: Pouze simulace bez ukládání změn
    """
    try:
        print(f"--- OPRAVA OBRÁZKŮ V OBSAHU ČLÁNKU WordPress ID: {wp_article_id}, PostgreSQL ID: {pg_article_id} ---")
        
        # Přip<PERSON>jení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        try:
            image_map = load_mapping('image_map.json')
            
            if not image_map:
                print("Chybí mapování obrázků.")
                print("Vytvářím prázdné mapování pro pokračování.")
                image_map = {}
            
            print(f"Načteno {len(image_map)} záznamů z image_map.json.")
        except Exception as e:
            print(f"Chyba při načítání mapování image_map.json: {e}")
            print("Vytvářím prázdné mapování pro pokračování.")
            image_map = {}
        
        # Získat unique_id článku
        pg_cursor.execute(f"""
            SELECT unikatni_id, nazev, text
            FROM {sabre_prefix}clanek 
            WHERE id_clanek = %s
        """, (pg_article_id,))
        
        article_info = pg_cursor.fetchone()
        if not article_info:
            print(f"Článek s ID {pg_article_id} nebyl nalezen v PostgreSQL.")
            return
        
        unique_id, article_title, pg_content = article_info
        print(f"Článek ID {pg_article_id}: {article_title} (unique_id: {unique_id})")
        
        # Získat obsah článku z WordPress
        mysql_cursor.execute(f"""
            SELECT ID, post_content
            FROM {wp_prefix}posts
            WHERE ID = %s
        """, (wp_article_id,))
        
        wp_article = mysql_cursor.fetchone()
        if not wp_article:
            print(f"Článek s ID {wp_article_id} nebyl nalezen ve WordPress.")
            return
        
        wp_content = wp_article['post_content']
        
        # Cesty k obrázkům
        image_base_path = get_image_base_path()
        wp_uploads_path = get_wp_uploads_path()
        wp_site_url = get_wp_site_url()
        default_wp_user_id = get_default_wp_user_id()
        
        # Zkusit najít adresář s obrázky - nejprve podle unique_id
        article_image_dir = os.path.join(image_base_path, unique_id)
        
        if not os.path.exists(article_image_dir):
            # Zkusit najít adresář podle regexp ve tvaru prvních 8 znaků unique_id
            if len(unique_id) >= 8:
                prefix = unique_id[:8]
                # Hledat potenciální adresáře v image_base_path
                matches = []
                try:
                    for dir_name in os.listdir(image_base_path):
                        if dir_name.startswith(prefix):
                            potential_dir = os.path.join(image_base_path, dir_name)
                            if os.path.isdir(potential_dir):
                                matches.append(potential_dir)
                except Exception as e:
                    print(f"Chyba při hledání adresářů: {e}")
                
                if matches:
                    print(f"Nalezeny alternativní adresáře: {matches}")
                    article_image_dir = matches[0]  # Použít první odpovídající
                    print(f"Používám alternativní adresář: {article_image_dir}")
                else:
                    print(f"Adresář s obrázky {article_image_dir} neexistuje a nebyla nalezena žádná alternativa.")
                    return
            else:
                print(f"Adresář s obrázky {article_image_dir} neexistuje.")
                return
        
        # Získat všechny fyzické soubory
        files = os.listdir(article_image_dir)
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
        
        # Předpočítat velikosti souborů
        file_sizes = {}
        for phys_file in image_files:
            full_path = os.path.join(article_image_dir, phys_file)
            file_sizes[phys_file] = os.path.getsize(full_path) if os.path.isfile(full_path) else 0
        
        # Hledat obrázky v obsahu
        # Regular expression pro hledání cest k obrázkům v HTML tagu <img>
        img_pattern = re.compile(r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>')
        img_matches = img_pattern.findall(wp_content)
        
        print(f"\nNalezeno {len(img_matches)} odkazů na obrázky v obsahu článku:")
        for i, src in enumerate(img_matches, 1):
            print(f"{i}. {src}")
        
        # Zkontrolovat, zda jsou všechny obrázky dostupné ve WordPress
        missing_images = []
        fixed_count = 0
        
        # Projít všechny obrázky v textu a zkontrolovat jejich dostupnost
        for src in img_matches:
            # Získat název souboru ze src
            filename = os.path.basename(src)
            basename = os.path.splitext(filename)[0]
            
            # Zkontrolovat, zda je obrázek v WordPress
            mysql_cursor.execute(f"""
                SELECT ID, guid
                FROM {wp_prefix}posts
                WHERE post_type = 'attachment'
                AND (guid = %s OR guid LIKE %s)
            """, (src, f"%{basename}%"))
            
            wp_image = mysql_cursor.fetchone()
            
            if wp_image:
                print(f"Obrázek {filename} již existuje ve WordPress (ID: {wp_image['ID']}).")
                continue
            
            # Obrázek chybí, přidat ho do seznamu k opravě
            missing_images.append((src, filename, basename))
        
        print(f"\nNalezeno {len(missing_images)} chybějících obrázků v obsahu článku.")
        
        if missing_images:
            # Pro každý chybějící obrázek najít odpovídající fyzický soubor a migrovat ho
            replacement_map = {}  # Původní URL -> nová URL
            
            for src, filename, basename in missing_images:
                # Najít všechny potenciálně odpovídající soubory
                matched_files = []
                for phys_file in image_files:
                    phys_base = os.path.splitext(phys_file)[0]
                    if phys_base.startswith(basename) or basename in phys_base or basename.lower() in phys_base.lower():
                        matched_files.append(phys_file)
                
                if not matched_files:
                    print(f"Pro soubor {filename} (src: {src}) nebyl nalezen odpovídající fyzický soubor.")
                    continue
                
                # Seřadit soubory podle velikosti sestupně (preferovat větší)
                matched_files.sort(key=lambda x: file_sizes.get(x, 0), reverse=True)
                
                # Filtrovat miniatury a malé verze
                filtered_matches = [f for f in matched_files if not (
                    '_wm' in f or  # vodoznak
                    '_wm_wm' in f or  # dvojitý vodoznak
                    ('_' in f and any(x.isdigit() for x in f))  # číselné přípony značící rozměry
                )]
                
                # Pokud po filtrování nic nezbylo, použij původní seznam
                final_matches = filtered_matches if filtered_matches else matched_files
                best_match = final_matches[0] if final_matches else None
                
                if not best_match:
                    print(f"Nebyl nalezen vhodný fyzický soubor pro {filename} (src: {src}).")
                    continue
                
                # Klíč pro mapování
                key_with_unique_id = f"{unique_id}/{best_match}"
                
                # Zkontrolovat, zda již není v mapování
                if key_with_unique_id in image_map:
                    wp_id = image_map[key_with_unique_id].get('wp_id')
                    wp_url = image_map[key_with_unique_id].get('wp_url')
                    
                    if wp_id and wp_url:
                        replacement_map[src] = wp_url
                        print(f"Obrázek {filename} (src: {src}) bude nahrazen z mapování: {wp_url} (ID: {wp_id})")
                        continue
                
                # Migrovat obrázek do WordPress
                if not dry_run:
                    full_path = os.path.join(article_image_dir, best_match)
                    new_wp_id = migrate_image_to_wordpress(
                        full_path, mysql_cursor, wp_prefix, wp_uploads_path, wp_site_url, default_wp_user_id
                    )
                    
                    if new_wp_id:
                        # Připravit nové URL a aktualizovat mapování
                        year_month = datetime.now().strftime('%Y/%m')
                        dest_rel_path = f"{year_month}/{best_match}"
                        new_url = f"{wp_site_url}/wp-content/uploads/{dest_rel_path}"
                        
                        image_map[key_with_unique_id] = {
                            'wp_id': new_wp_id,
                            'wp_path': dest_rel_path,
                            'wp_url': new_url
                        }
                        
                        replacement_map[src] = new_url
                        fixed_count += 1
                        print(f"Obrázek {filename} (src: {src}) migrován jako: {new_url} (ID: {new_wp_id})")
                else:
                    print(f"[DRY-RUN] Obrázek {filename} (src: {src}) by byl migrován jako: {best_match}")
                    fixed_count += 1
            
            # Aktualizovat obsah článku s novými URL obrázků
            new_content = wp_content
            if replacement_map and not dry_run:
                # Postupně nahradit všechny src
                for old_src, new_src in replacement_map.items():
                    new_content = new_content.replace(f'src="{old_src}"', f'src="{new_src}"')
                    new_content = new_content.replace(f"src='{old_src}'", f"src='{new_src}'")
                
                # Aktualizovat obsah článku v databázi
                mysql_cursor.execute(f"""
                    UPDATE {wp_prefix}posts
                    SET post_content = %s
                    WHERE ID = %s
                """, (new_content, wp_article_id))
                
                mysql_conn.commit()
                
                # Uložit aktualizované mapování
                save_mapping(image_map, 'image_map.json')
                print(f"\nAktualizován obsah článku, nahrazeno {len(replacement_map)} odkazů na obrázky.")
                print("Mapování obrázků uloženo.")
            
            elif dry_run:
                print(f"\n[DRY-RUN] Aktualizace obsahu článku: nahradilo by se {fixed_count} odkazů na obrázky.")
            
            else:
                print("\nŽádné obrázky nebyly nahrazeny v obsahu článku.")
        
    except Exception as e:
        print(f"Chyba: {e}")
        if not dry_run and 'mysql_conn' in locals():
            mysql_conn.rollback()
        import traceback
        traceback.print_exc()
    finally:
        # Zavřít spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

def fix_post_content_images_for_all_articles(limit=None, start_from=0, dry_run=False, verbose=False):
    """
    Opraví obrázky v obsahu všech článků.
    
    Args:
        limit: Maximální počet článků ke zpracování
        start_from: Index, od kterého začít
        dry_run: Pouze simulace bez ukládání změn
        verbose: Podrobnější výpisy
    """
    try:
        print(f"--- SPOUŠTÍM OPRAVU OBRÁZKŮ V OBSAHU VŠECH ČLÁNKŮ ---")
        print(f"Parametry: limit={limit}, start_from={start_from}, dry_run={dry_run}, verbose={verbose}")
        
        # Připojení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        article_map = load_mapping('article_map.json')
        if not article_map:
            print("Chybí mapování článků.")
            return
        
        # Získat seznam článků s obrázky v obsahu
        pg_cursor.execute(f"""
            SELECT c.id_clanek, c.unikatni_id, c.nazev, c.text
            FROM {sabre_prefix}clanek c
            WHERE c.text LIKE '%<img%' AND c.active_state = 1
            ORDER BY c.id_clanek DESC
        """)
        
        articles = pg_cursor.fetchall()
        print(f"Nalezeno {len(articles)} článků s obrázky v obsahu.")
        
        # Statistiky
        total_processed = 0
        total_fixed = 0
        total_skipped = 0
        
        # Zpracovat každý článek
        for i, (pg_id, unique_id, title, content) in enumerate(articles[start_from:], start=start_from):
            if limit and total_processed >= limit:
                print(f"Dosažen limit {limit} článků, končím.")
                break
            
            # Najít WordPress ID článku z mapování
            wp_id = article_map.get(str(pg_id))
            if not wp_id:
                print(f"Pro PostgreSQL článek {pg_id} ({title}) nebyl nalezen WordPress ekvivalent.")
                total_skipped += 1
                continue
            
            # Zkontrolovat existenci článku ve WordPress
            mysql_cursor.execute(f"""
                SELECT ID
                FROM {wp_prefix}posts
                WHERE ID = %s AND post_type = 'post'
            """, (wp_id,))
            
            if not mysql_cursor.fetchone():
                print(f"WordPress článek ID {wp_id} neexistuje nebo není typu 'post'.")
                total_skipped += 1
                continue
            
            if verbose:
                print(f"\n{'-' * 80}")
                print(f"Zpracovávám článek {i+1}/{len(articles)}: PG ID {pg_id}, WP ID {wp_id} - {title}")
            
            # Opravit obrázky v obsahu
            try:
                if verbose:
                    # Při verbose zobrazit podrobné informace
                    fix_post_content_images_for_article(wp_id, pg_id, dry_run)
                else:
                    # V tichém režimu redirectovat standardní výstup
                    import io
                    import sys
                    original_stdout = sys.stdout
                    sys.stdout = io.StringIO()
                    
                    try:
                        fix_post_content_images_for_article(wp_id, pg_id, dry_run)
                        output = sys.stdout.getvalue()
                        
                        # Zkontrolovat výstup na přítomnost oprav
                        if "migrován jako" in output or "nahrazeno" in output:
                            print(f"Opraven článek PG ID {pg_id}, WP ID {wp_id} ({title})")
                            total_fixed += 1
                        else:
                            print(f"Článek PG ID {pg_id}, WP ID {wp_id} ({title}) - žádné změny")
                            total_skipped += 1
                            
                    finally:
                        sys.stdout = original_stdout
                
                total_processed += 1
                
            except Exception as e:
                print(f"Chyba při zpracování článku PG ID {pg_id}, WP ID {wp_id} ({title}): {e}")
                import traceback
                traceback.print_exc()
        
        # Vypsat statistiky
        print(f"\n{'-' * 80}")
        print(f"SOUHRN OPRAVY OBRÁZKŮ V OBSAHU ČLÁNKŮ:")
        print(f"Celkem zpracováno článků: {total_processed}")
        print(f"Celkem opraveno článků: {total_fixed}")
        print(f"Celkem přeskočeno článků: {total_skipped}")
        
    except Exception as e:
        print(f"Obecná chyba: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Zavřít spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Oprava obrázků v obsahu článků')
    parser.add_argument('--wp-id', type=int, help='WordPress ID článku (pro opravu jednoho konkrétního článku)')
    parser.add_argument('--pg-id', type=int, help='PostgreSQL ID článku (pro opravu jednoho konkrétního článku)')
    parser.add_argument('--all', action='store_true', help='Opravit obrázky ve všech článcích')
    parser.add_argument('--limit', type=int, help='Maximální počet článků ke zpracování')
    parser.add_argument('--start-from', type=int, default=0, help='Index, od kterého začít')
    parser.add_argument('--dry-run', action='store_true', help='Pouze simulace bez ukládání změn')
    parser.add_argument('--verbose', action='store_true', help='Podrobnější výpisy')
    
    args = parser.parse_args()
    
    if args.wp_id and args.pg_id:
        # Opravit konkrétní článek
        fix_post_content_images_for_article(args.wp_id, args.pg_id, args.dry_run)
    elif args.all:
        # Opravit všechny články
        fix_post_content_images_for_all_articles(args.limit, args.start_from, args.dry_run, args.verbose)
    else:
        parser.print_help()
