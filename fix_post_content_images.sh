#!/bin/bash

# Skript pro spuštění opravy obrázků v obsahu článků

# Nastavení cesty k logovacímu adresáři
LOG_DIR="logs"
mkdir -p $LOG_DIR

# Datum a čas pro název logu
DATE=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/fix_post_content_images_$DATE.log"

echo "Spouštím opravu obrázků v obsahu článků..."
echo "Výsledky budou uloženy do $LOG_FILE"

# Nejprve provedeme test s dry-run a limitem
echo "Provádím test (dry-run) s limitem 5 článků..."
python3 fix_post_content_images.py --all --dry-run --limit 5 --verbose | tee "$LOG_FILE.test"

# Zeptáme se, zda má pokračovat s opravou všech článků
echo
echo "Test byl dokončen. Chcete pokračovat s opravou všech článků? (y/n)"
read -p "> " choice
if [[ "$choice" == "y" || "$choice" == "Y" ]]; then
    echo "Spouštím opravu všech článků..."
    python3 fix_post_content_images.py --all --verbose | tee $LOG_FILE
    echo
    echo "Oprava dokončena. Log je uložen v: $LOG_FILE"
else
    echo "Oprava byla zrušena."
    echo "Pro provedení opravy spusťte:"
    echo "  python3 fix_post_content_images.py --all"
    echo "nebo pro opravu jednoho článku:"
    echo "  python3 fix_post_content_images.py --wp-id ID_CLANKU_WP --pg-id ID_CLANKU_PG"
fi
