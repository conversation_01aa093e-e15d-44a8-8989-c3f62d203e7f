#!/usr/bin/env python3
import logging
from db_connectors import get_pg_connection
from config import SABRE_TABLE_PREFIX

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_gallery_tables():
    """<PERSON><PERSON><PERSON><PERSON><PERSON>, jak<PERSON> tabulky související s galeriemi existují."""
    logging.info("Kontroluji tabulky související s galeriemi...")
    
    pg_conn = get_pg_connection()
    pg_cursor = pg_conn.cursor()
    
    try:
        # Získat všechny tabulky s prefixem
        pg_cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE %s
            ORDER BY table_name
        """, (f"{SABRE_TABLE_PREFIX}%",))
        
        all_tables = [row[0] for row in pg_cursor.fetchall()]
        
        logging.info("Všechny tabulky s prefixem:")
        for table in all_tables:
            logging.info(f"  - {table}")
        
        # Hledat tabulky související s galeriemi
        gallery_tables = [t for t in all_tables if 'galer' in t.lower()]
        
        logging.info("\nTabulky související s galeriemi:")
        if gallery_tables:
            for table in gallery_tables:
                logging.info(f"  ✓ {table}")
                
                # Zkontrolovat strukturu tabulky
                pg_cursor.execute(f"""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = '{table}'
                    ORDER BY ordinal_position
                """)
                columns = pg_cursor.fetchall()
                logging.info(f"    Sloupce: {', '.join([col[0] for col in columns])}")
        else:
            logging.warning("  ✗ Žádné tabulky související s galeriemi nebyly nalezeny!")
        
        # Zkontrolovat tabulku obrazek pro galerie
        obrazek_table = f"{SABRE_TABLE_PREFIX}obrazek"
        if obrazek_table in all_tables:
            logging.info(f"\nKontroluji tabulku {obrazek_table} pro galerie...")
            
            # Zkontrolovat, jestli má sloupce pro galerie
            pg_cursor.execute(f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = '{obrazek_table}'
                AND column_name IN ('polozka_id', 'typ', 'priorita')
                ORDER BY ordinal_position
            """)
            gallery_columns = pg_cursor.fetchall()
            
            if gallery_columns:
                logging.info("  ✓ Tabulka obrazek obsahuje sloupce pro galerie:")
                for col_name, col_type in gallery_columns:
                    logging.info(f"    - {col_name} ({col_type})")
                
                # Zkontrolovat typy obrázků
                pg_cursor.execute(f"""
                    SELECT typ, COUNT(*) as pocet
                    FROM {obrazek_table}
                    WHERE typ IS NOT NULL
                    GROUP BY typ
                    ORDER BY pocet DESC
                """)
                types = pg_cursor.fetchall()
                
                logging.info("  Typy obrázků v databázi:")
                for typ, pocet in types:
                    logging.info(f"    - {typ}: {pocet} obrázků")
            else:
                logging.warning("  ✗ Tabulka obrazek neobsahuje potřebné sloupce pro galerie")
        
    except Exception as e:
        logging.error(f"Chyba při kontrole tabulek: {e}")
    finally:
        pg_cursor.close()
        pg_conn.close()

if __name__ == "__main__":
    check_gallery_tables()
