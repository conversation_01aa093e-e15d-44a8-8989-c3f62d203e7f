#!/bin/bash

# Skript pro spuštění opravy galerií pomocí PostgreSQL dat

echo "Spouštím opravu galerií s chybějícími obrázky pomocí PostgreSQL..."
echo ""

# <PERSON><PERSON><PERSON><PERSON>, zda je Python nainstalován
if ! command -v python3 &> /dev/null; then
    echo "Python 3 není nainstalován. Prosím, nainstalujte Python 3 a zkuste to znovu."
    exit 1
fi

# Kontrola, zda existuje soubor s opravou
if [ ! -f "fix_galleries_postgres.py" ]; then
    echo "Soubor fix_galleries_postgres.py nebyl nalezen v aktuálním adresáři."
    exit 1
fi

# Kontrola, zda jsou nainstalované potřebné balíčky
python3 -c "import mysql.connector" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Balíček mysql-connector-python není nainstalován."
    echo "Instaluji..."
    pip3 install mysql-connector-python
    
    # Kontrola, zda se instalace povedla
    python3 -c "import mysql.connector" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Instalace mysql-connector-python selhala. Zkuste ho nainstalovat ručně:"
        echo "pip3 install mysql-connector-python"
        exit 1
    fi
fi

python3 -c "import psycopg2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Balíček psycopg2 není nainstalován."
    echo "Instaluji..."
    pip3 install psycopg2-binary
    
    # Kontrola, zda se instalace povedla
    python3 -c "import psycopg2" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "Instalace psycopg2 selhala. Zkuste ho nainstalovat ručně:"
        echo "pip3 install psycopg2-binary"
        exit 1
    fi
fi

# Nastavení oprávnění pro spuštění
chmod +x fix_galleries_postgres.py

# Spuštění skriptu pro opravu
python3 fix_galleries_postgres.py

# Kontrola, zda skript proběhl úspěšně
if [ $? -ne 0 ]; then
    echo ""
    echo "Oprava selhala. Podívejte se do logů pro více informací."
    exit 1
fi

echo ""
echo "Oprava dokončena. Podívejte se do logů pro detail informací o opravě."
echo "Log soubor: logs/fix_gallery_images_postgres_*.log"
