#!/bin/bash

# Komplexní skript pro opravu všech problémů s obrázky
# 1. Opraví galerie (malé obrázky a duplicity)
# 2. Opraví obrázky v obsahu článků

# Nastavení cesty k logovacímu adresáři
LOG_DIR="logs"
mkdir -p $LOG_DIR

# Datum a čas pro název logu
DATE=$(date +"%Y%m%d_%H%M%S")
GALLERY_LOG="$LOG_DIR/fix_all_galleries_systematic_$DATE.log"
CONTENT_LOG="$LOG_DIR/fix_post_content_images_$DATE.log"

# Zobrazení hlavního menu
show_menu() {
  echo "==============================================================="
  echo "                 OPRAVA PROBLÉMŮ S OBRÁZKY"
  echo "==============================================================="
  echo "Vyberte operaci:"
  echo "1) Opravit všechny galerie (preferovat větší obrázky, odstranit duplicity)"
  echo "2) Opravit obrázky v obsahu článků (vložené obrázky v textu)"
  echo "3) Spustit obě operace postupně"
  echo "4) Spustit dry-run test (simulace bez ukládání změn)"
  echo "q) Ukončit"
  echo "==============================================================="
}

# Funkce pro opravu galerií
fix_galleries() {
  echo "Spouštím opravu všech galerií..."
  echo "Výsledky budou uloženy do $GALLERY_LOG"
  
  python3 fix_all_galleries_systematic.py --verbose | tee $GALLERY_LOG
  
  echo
  echo "Oprava galerií dokončena. Log je uložen v: $GALLERY_LOG"
}

# Funkce pro opravu obrázků v obsahu
fix_content_images() {
  echo "Spouštím opravu obrázků v obsahu článků..."
  echo "Výsledky budou uloženy do $CONTENT_LOG"
  
  python3 fix_post_content_images.py --all --verbose | tee $CONTENT_LOG
  
  echo
  echo "Oprava obrázků v obsahu dokončena. Log je uložen v: $CONTENT_LOG"
}

# Funkce pro testovací běh (dry-run)
run_tests() {
  echo "Provádím testovací běh (dry-run)..."
  
  # Test galerií s limitem 3 článků
  echo "Test opravy galerií (3 články)..."
  python3 fix_all_galleries_systematic.py --dry-run --limit 3 --verbose | tee "$GALLERY_LOG.test"
  
  echo
  echo "Test opravy obrázků v obsahu (3 články)..."
  python3 fix_post_content_images.py --all --dry-run --limit 3 --verbose | tee "$CONTENT_LOG.test"
  
  echo
  echo "Testovací běh dokončen."
  echo "Logy testů jsou uloženy v: $GALLERY_LOG.test a $CONTENT_LOG.test"
}

# Hlavní smyčka
while true; do
  show_menu
  read -p "Zadejte volbu [1-4/q]: " choice
  
  case $choice in
    1)
      fix_galleries
      ;;
    2)
      fix_content_images
      ;;
    3)
      echo "Spouštím komplexní opravu všech problémů s obrázky..."
      fix_galleries
      echo
      echo "Pokračuji s opravou obrázků v obsahu..."
      fix_content_images
      echo
      echo "Komplexní oprava dokončena."
      ;;
    4)
      run_tests
      ;;
    q|Q)
      echo "Ukončuji program."
      exit 0
      ;;
    *)
      echo "Neplatná volba. Zkuste znovu."
      ;;
  esac
  
  echo
  read -p "Stiskněte Enter pro návrat do menu..."
done
