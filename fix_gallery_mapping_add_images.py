#!/usr/bin/env python3
import os
import json
import mysql.connector
import psycopg2
import sys
from datetime import datetime
import shutil
import mimetypes

def get_mime_type(file_path):
    """Získá MIME typ souboru."""
    mime_type, encoding = mimetypes.guess_type(file_path)
    if mime_type is None:
        # Výchozí MIME typ pro obrázky
        ext = os.path.splitext(file_path)[1].lower()
        if ext == '.jpg' or ext == '.jpeg':
            return 'image/jpeg'
        elif ext == '.png':
            return 'image/png'
        elif ext == '.gif':
            return 'image/gif'
        return 'application/octet-stream'
    return mime_type

def format_wp_datetime(date_obj):
    """Formátuje datum pro WordPress."""
    if date_obj is None:
        date_obj = datetime.now()
    return date_obj.strftime('%Y-%m-%d %H:%M:%S')

def format_wp_datetime_gmt(date_obj):
    """Formátuje GMR datum pro WordPress."""
    return format_wp_datetime(date_obj)

def load_mapping(filename):
    """Načte mapování ze souboru."""
    try:
        filepath = os.path.join('mappings', filename)
        if not os.path.exists(filepath):
            return {}
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Chyba při načítání mapování {filename}: {e}")
        return {}

def save_mapping(data, filename):
    """Uloží mapování do souboru."""
    try:
        os.makedirs('mappings', exist_ok=True)
        filepath = os.path.join('mappings', filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Chyba při ukládání mapování {filename}: {e}")
        return False

def get_pg_connection():
    """Vytvoří a vrátí spojení s PostgreSQL databází pro SABRE"""
    pg_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('PG_'):
                    pg_config[key[3:].lower()] = value

    return psycopg2.connect(
        host=pg_config.get('host', 'localhost'),
        user=pg_config.get('user', ''),
        password=pg_config.get('password', ''),
        database=pg_config.get('dbname', ''),
        port=int(pg_config.get('port', 5432))
    )

def get_mysql_connection():
    """Vytvoří a vrátí spojení s MySQL databází pro WordPress"""
    mysql_config = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                key, value = line.split('=', 1)
                if key.startswith('MYSQL_'):
                    mysql_config[key[6:].lower()] = value

    return mysql.connector.connect(
        host=mysql_config.get('host', 'localhost'),
        user=mysql_config.get('user', ''),
        password=mysql_config.get('password', ''),
        database=mysql_config.get('dbname', ''),
        port=int(mysql_config.get('port', 3306))
    )

def get_table_prefixes():
    """Vrátí prefixy tabulek."""
    wp_prefix = 'dumabyt_'
    sabre_prefix = 'prefix_'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_TABLE_PREFIX='):
                    wp_prefix = line.split('=', 1)[1]
                elif line.startswith('SABRE_TABLE_PREFIX='):
                    sabre_prefix = line.split('=', 1)[1]
    
    return wp_prefix, sabre_prefix

def get_image_base_path():
    """Získá základní cestu k obrázkům."""
    path = '/home/<USER>/sabre/public_html_backup/public_html/obrazek'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('OLD_IMAGE_BASE_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def get_wp_uploads_path():
    """Získá cestu k WordPress uploads adresáři."""
    path = '/home/<USER>/www/dumabyt/wp-content/uploads'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_UPLOADS_PATH='):
                    path = line.split('=', 1)[1]
    
    return path

def get_wp_site_url():
    """Získá URL WordPress webu."""
    url = 'http://dumabyt.test'
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('WP_SITE_URL='):
                    url = line.split('=', 1)[1].rstrip('/')
    
    return url

def get_default_wp_user_id():
    """Získá výchozí WordPress User ID."""
    user_id = 1
    
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('DEFAULT_WP_USER_ID='):
                    user_id = int(line.split('=', 1)[1])
    
    return user_id

def ensure_dir_exists(path):
    """Zajistí existenci adresáře."""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Chyba při vytváření adresáře {path}: {e}")
        return False

def migrate_image_to_wordpress(source_path, mysql_cursor, wp_prefix, wp_uploads_path, wp_site_url, default_wp_user_id):
    """Migruje obrázek do WordPress a vrátí jeho WordPress ID."""
    try:
        # Vytvořit cílovou cestu ve WordPress uploads adresáři
        year_month = datetime.now().strftime('%Y/%m')
        filename = os.path.basename(source_path)
        dest_rel_path = f"{year_month}/{filename}"
        dest_path = os.path.join(wp_uploads_path, dest_rel_path)
        
        # Ujistit se, že cílový adresář existuje
        dest_dir = os.path.dirname(dest_path)
        if not ensure_dir_exists(dest_dir):
            print(f"Nelze vytvořit cílový adresář pro {source_path}, přeskakuji.")
            return None
        
        # Kopírovat soubor
        try:
            shutil.copy2(source_path, dest_path)
            print(f"Úspěšně zkopírován soubor {source_path} do {dest_path}")
        except Exception as e:
            print(f"Chyba při kopírování souboru '{source_path}': {e}")
            return None
        
        # Získat MIME typ
        mime_type = get_mime_type(dest_path)
        
        # Připravit data pro wp_posts
        post_date = format_wp_datetime(datetime.now())
        post_date_gmt = format_wp_datetime_gmt(datetime.now())
        post_title = os.path.splitext(filename)[0].replace('-', ' ').replace('_', ' ').title()
        guid = f"{wp_site_url}/wp-content/uploads/{dest_rel_path}"
        
        # Vložit do wp_posts
        sql_posts = f"""
            INSERT INTO {wp_prefix}posts
            (post_author, post_date, post_date_gmt, post_content, post_title, post_excerpt,
            post_status, comment_status, ping_status, post_name, post_modified, post_modified_gmt,
            post_parent, guid, menu_order, post_type, post_mime_type, comment_count,
            to_ping, pinged, post_content_filtered)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        mysql_cursor.execute(sql_posts, (
            default_wp_user_id, post_date, post_date_gmt, '', post_title, '',
            'inherit', 'closed', 'closed', post_title.lower().replace(' ', '-'),
            post_date, post_date_gmt, 0, guid, 0, 'attachment', mime_type, 0,
            '', '', ''
        ))
        attachment_id = mysql_cursor.lastrowid
        
        # Vložit metadata
        sql_meta = f"INSERT INTO {wp_prefix}postmeta (post_id, meta_key, meta_value) VALUES (%s, %s, %s)"
        mysql_cursor.execute(sql_meta, (attachment_id, '_wp_attached_file', dest_rel_path))
        
        print(f"Obrázek '{filename}' úspěšně migrován s ID: {attachment_id}")
        return attachment_id
    
    except Exception as e:
        print(f"Chyba při migraci obrázku '{source_path}': {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_gallery_for_article(wp_article_id=67797, pg_article_id=29152, dry_run=False):
    try:
        print(f"--- OPRAVA GALERIE PRO ČLÁNEK WordPress ID: {wp_article_id}, PostgreSQL ID: {pg_article_id} ---")
        
        # Připojení k databázím
        pg_conn = get_pg_connection()
        mysql_conn = get_mysql_connection()
        pg_cursor = pg_conn.cursor()
        mysql_cursor = mysql_conn.cursor(dictionary=True)
        
        # Získání prefixů tabulek
        wp_prefix, sabre_prefix = get_table_prefixes()
        
        # Načtení mapování
        image_map = load_mapping('image_map.json')
        
        if not image_map:
            print("Chybí mapování obrázků.")
            return
        
        print(f"Načteno {len(image_map)} záznamů z image_map.json.")
        
        # Získat unique_id článku
        pg_cursor.execute(f"""
            SELECT unikatni_id, nazev 
            FROM {sabre_prefix}clanek 
            WHERE id_clanek = %s
        """, (pg_article_id,))
        
        article_info = pg_cursor.fetchone()
        if not article_info:
            print(f"Článek s ID {pg_article_id} nebyl nalezen v PostgreSQL.")
            return
        
        unique_id, article_title = article_info
        print(f"Článek ID {pg_article_id}: {article_title} (unique_id: {unique_id})")
        
        # Získat obrázky článku z PostgreSQL
        pg_cursor.execute(f"""
            SELECT id_obrazek, soubor, priorita, active_state
            FROM {sabre_prefix}obrazek 
            WHERE polozka_id = %s AND active_state = 1 AND typ = 1
            ORDER BY priorita DESC, id_obrazek
        """, (unique_id,))
        
        obrazky = pg_cursor.fetchall()
        print(f"\nNalezeno {len(obrazky)} obrázků v PostgreSQL:")
        for i, (img_id, img_file, img_prio, img_active) in enumerate(obrazky, 1):
            print(f"{i}. ID={img_id}, Soubor={img_file}, Priorita={img_prio}, Aktivní={img_active}")
        
        # Získat metadata galerie
        mysql_cursor.execute(f"""
            SELECT meta_id, meta_value
            FROM {wp_prefix}postmeta
            WHERE post_id = %s AND meta_key = 'sabre_gallery'
        """, (wp_article_id,))
        
        gallery_meta = mysql_cursor.fetchone()
        if not gallery_meta:
            print("\nČlánek nemá žádná metadata sabre_gallery.")
            return
        
        gallery_data = json.loads(gallery_meta['meta_value'])
        print(f"\nPůvodní metadata galerie:")
        print(f"Počet obrázků v metadatech: {gallery_data.get('image_count', 0)}")
        print(f"Image IDs v metadatech: {gallery_data.get('image_ids', [])}")
        
        # Zjistit jaké obrázky jsou již v galerii
        existing_wp_ids = gallery_data.get('image_ids', [])
        
        # Zkontrolovat fyzické soubory ve složce článku
        image_base_path = get_image_base_path()
        wp_uploads_path = get_wp_uploads_path()
        wp_site_url = get_wp_site_url()
        default_wp_user_id = get_default_wp_user_id()
        
        article_image_dir = os.path.join(image_base_path, unique_id)
        
        if not os.path.exists(article_image_dir):
            print(f"Adresář s obrázky {article_image_dir} neexistuje.")
            return
        
        files = os.listdir(article_image_dir)
        # Filtrovat náhledy a pomocné obrázky
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) 
                      and not any(f.endswith(suffix) for suffix in (
                          '_100x67.jpg', '_120x80.jpg', '_68x90.jpg', '_100x66.jpg', '_100x57.jpg', 
                          '_635x363.jpg', '_639x365.jpg', '_700x400.jpg', '_303x173.jpg', '_120x69.jpg'))]
        
        print(f"\nNalezeno {len(image_files)} fyzických obrázkových souborů (bez náhledů):")
        for i, img_file in enumerate(image_files, 1):
            full_path = os.path.join(article_image_dir, img_file)
            file_size = os.path.getsize(full_path) if os.path.isfile(full_path) else 0
            print(f"{i}. {img_file} (velikost: {file_size} bajtů)")
        
        # Mapování PostgreSQL obrázků na fyzické soubory
        print("\nMapování PostgreSQL souborů na fyzické soubory:")
        pg_to_physical = {}
        
        # Předpočítat velikosti souborů
        file_sizes = {}
        for phys_file in image_files:
            full_path = os.path.join(article_image_dir, phys_file)
            file_sizes[phys_file] = os.path.getsize(full_path) if os.path.isfile(full_path) else 0
        
        for img_id, img_file, _, _ in obrazky:
            base_name = os.path.splitext(img_file)[0]
            matched_files = []
            
            # Najít všechny potenciálně odpovídající soubory
            for phys_file in image_files:
                phys_base = os.path.splitext(phys_file)[0]
                if phys_base.startswith(base_name) or base_name in phys_base:
                    matched_files.append(phys_file)
            
            # Seřadit soubory podle velikosti sestupně (preferovat větší)
            matched_files.sort(key=lambda x: file_sizes.get(x, 0), reverse=True)
            
            # Filtrovat miniatury a malé verze (pokud existují větší alternativy)
            filtered_matches = [f for f in matched_files if not (
                 '_wm' in f or  # vodoznak
                 '_wm_wm' in f or  # dvojitý vodoznak
                 ('_' in f and any(x.isdigit() for x in f))  # číselné přípony značící rozměry
             )]
            
            # Pokud po filtrování nic nezbylo, použij původní seznam
            final_matches = filtered_matches if filtered_matches else matched_files
            
            # Vybrat nejlepší soubor (největší a bez přípony rozměrů, pokud existuje)
            matched_file = final_matches[0] if final_matches else None
            
            pg_to_physical[img_file] = matched_file
            
            if matched_file and len(matched_files) > 1:
                print(f"- PostgreSQL: {img_file} -> Fyzický soubor: {matched_file} (vybráno z {len(matched_files)} možností, velikost: {file_sizes[matched_file]} bajtů)")
            else:
                print(f"- PostgreSQL: {img_file} -> Fyzický soubor: {matched_file}")
        
        # Najít WordPress ID pro všechny obrázky
        wp_image_ids = set(existing_wp_ids)  # Začneme s existujícími obrázky
        
        # Pro každý fyzický soubor
        for pg_file, physical_file in pg_to_physical.items():
            if not physical_file:
                print(f"Chybí fyzický soubor pro {pg_file}, přeskakuji.")
                continue
                
            full_path = os.path.join(article_image_dir, physical_file)
            
            # 1. Zkusit najít v image_map
            key_with_unique_id = f"{unique_id}/{physical_file}"
            if key_with_unique_id in image_map:
                wp_id = image_map[key_with_unique_id].get('wp_id')
                if wp_id:
                    wp_image_ids.add(str(wp_id))
                    print(f"- Nalezeno v image_map: {pg_file} -> {physical_file} -> WordPress ID: {wp_id}")
                    continue
            
            # 2. Zkusit najít podle názvu ve WordPress
            mysql_cursor.execute(f"""
                SELECT ID, post_title, guid
                FROM {wp_prefix}posts
                WHERE post_type = 'attachment'
                AND post_title LIKE %s
            """, (f"%{os.path.splitext(physical_file)[0]}%",))
            
            wp_images = mysql_cursor.fetchall()
            if wp_images:
                wp_id = wp_images[0]['ID']
                wp_image_ids.add(str(wp_id))
                print(f"- Nalezeno podle názvu: {pg_file} -> {physical_file} -> WordPress ID: {wp_id}")
                
                # Přidat do mapování, pokud tam chybí
                if key_with_unique_id not in image_map:
                    image_map[key_with_unique_id] = {
                        'wp_id': wp_id,
                        'wp_path': None,  # Neznáme relativní cestu
                        'wp_url': wp_images[0]['guid']
                    }
                    print(f"  - Přidáno do mapování: {key_with_unique_id}")
                continue
            
            # 3. Pokud obrázek nebyl nalezen, je třeba ho migrovat
            print(f"- Nenalezeno, bude migrováno: {pg_file} -> {physical_file}")
            
            if not dry_run:
                # Migrovat obrázek do WordPress
                new_wp_id = migrate_image_to_wordpress(
                    full_path, mysql_cursor, wp_prefix, wp_uploads_path, wp_site_url, default_wp_user_id
                )
                
                if new_wp_id:
                    # Přidat do seznamu ID
                    wp_image_ids.add(str(new_wp_id))
                    
                    # Aktualizovat mapování
                    year_month = datetime.now().strftime('%Y/%m')
                    dest_rel_path = f"{year_month}/{physical_file}"
                    guid = f"{wp_site_url}/wp-content/uploads/{dest_rel_path}"
                    
                    image_map[key_with_unique_id] = {
                        'wp_id': new_wp_id,
                        'wp_path': dest_rel_path,
                        'wp_url': guid
                    }
                    print(f"  - Přidáno do mapování: {key_with_unique_id}")
            else:
                print(f"  - Bude migrováno při skutečném běhu (mimo dry-run)")
        
        # Převést na seznam a seřadit podle priorit v PostgreSQL
        sorted_wp_image_ids = []
        added_ids = set()  # Sledovat již přidané ID proti duplikátům
        
        # 1. Nejprve přidat obrázky v pořadí podle PostgreSQL (priorita)
        for img_id, img_file, _, _ in obrazky:
            physical_file = pg_to_physical.get(img_file)
            if physical_file:
                key_with_unique_id = f"{unique_id}/{physical_file}"
                if key_with_unique_id in image_map:
                    wp_id = str(image_map[key_with_unique_id].get('wp_id'))
                    if wp_id and wp_id in wp_image_ids and wp_id not in added_ids:
                        sorted_wp_image_ids.append(wp_id)
                        added_ids.add(wp_id)
        
        # 2. Přidat zbývající ID, které nebyly přiřazeny k PostgreSQL obrázkům
        for wp_id in wp_image_ids:
            if wp_id not in added_ids:
                sorted_wp_image_ids.append(wp_id)
                added_ids.add(wp_id)
        
        print(f"\nNalezeno celkem {len(sorted_wp_image_ids)} unikátních WordPress ID obrázků: {sorted_wp_image_ids}")
        
        if len(sorted_wp_image_ids) <= 1:
            print("Nedostatek WordPress obrázků pro vytvoření galerie.")
            if dry_run:
                print("Zkuste spustit skript bez parametru --dry-run pro migraci chybějících obrázků.")
            return
        
        if not dry_run:
            # Aktualizovat mapování obrázků
            save_mapping(image_map, 'image_map.json')
            print("Mapování obrázků uloženo.")
            
            # Aktualizovat metadata sabre_gallery
            gallery_data['image_ids'] = sorted_wp_image_ids
            gallery_data['image_count'] = len(sorted_wp_image_ids)
            gallery_data['fixed_migration'] = True
            gallery_data['migration_timestamp'] = datetime.now().isoformat()
            
            mysql_cursor.execute(f"""
                UPDATE {wp_prefix}postmeta
                SET meta_value = %s
                WHERE meta_id = %s
            """, (json.dumps(gallery_data), gallery_meta['meta_id']))
            
            # Aktualizovat gallery shortcode v obsahu článku
            mysql_cursor.execute(f"""
                SELECT post_content
                FROM {wp_prefix}posts
                WHERE ID = %s
            """, (wp_article_id,))
            
            post_content = mysql_cursor.fetchone()['post_content']
            new_gallery_shortcode = f'[gallery ids="{",".join(sorted_wp_image_ids)}"]'
            
            # Najít všechny existující gallery shortcodes
            gallery_start = post_content.find('[gallery')
            if gallery_start >= 0:
                gallery_end = post_content.find(']', gallery_start)
                old_shortcode = post_content[gallery_start:gallery_end+1]
                post_content = post_content.replace(old_shortcode, new_gallery_shortcode)
            else:
                # Shortcode neexistuje, přidat ho na konec obsahu
                post_content += "\n\n" + new_gallery_shortcode
            
            mysql_cursor.execute(f"""
                UPDATE {wp_prefix}posts
                SET post_content = %s
                WHERE ID = %s
            """, (post_content, wp_article_id))
            
            mysql_conn.commit()
            print("\nGalerie úspěšně aktualizována:")
            print(f"- Metadata sabre_gallery aktualizována s {len(sorted_wp_image_ids)} obrázky")
            print(f"- Gallery shortcode aktualizován: {new_gallery_shortcode}")
        else:
            print("\nSimulace aktualizace (dry_run):")
            print(f"- Metadata by byla aktualizována s {len(sorted_wp_image_ids)} obrázky")
            print(f"- Gallery shortcode by byl: [gallery ids=\"{','.join(sorted_wp_image_ids)}\"]")
        
    except Exception as e:
        print(f"Chyba: {e}")
        if not dry_run and 'mysql_conn' in locals():
            mysql_conn.rollback()
        import traceback
        traceback.print_exc()
    finally:
        # Zavřít spojení
        if 'pg_cursor' in locals():
            pg_cursor.close()
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'pg_conn' in locals():
            pg_conn.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Oprava galerie pro konkrétní článek včetně migrace chybějících obrázků')
    parser.add_argument('--wp-id', type=int, default=67797, help='WordPress ID článku')
    parser.add_argument('--pg-id', type=int, default=29152, help='PostgreSQL ID článku')
    parser.add_argument('--dry-run', action='store_true', help='Pouze simulovat opravy bez ukládání')
    args = parser.parse_args()
    
    fix_gallery_for_article(args.wp_id, args.pg_id, args.dry_run)
