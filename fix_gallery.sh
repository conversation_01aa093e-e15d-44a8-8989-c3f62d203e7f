#!/bin/bash

# Skript pro opravu galerie ve WordPress pomocí specifického řešení
# pro adresářovou strukturu obrázků

echo "=== Nástroj pro opravu migrace galerií ==="
echo

# Kontrola zda jsme ve správném adresáři
if [ ! -f "fix_gallery_28931.py" ]; then
    echo "CHYBA: Script musí být spuštěn z ad<PERSON>áře, kde se nachází fix_gallery_28931.py"
    exit 1
fi

# Výchozí hodnoty
ARTICLE_ID="28931"
RESET_MODE=false

# Zpracování argumentů
while [[ $# -gt 0 ]]; do
    case "$1" in
        --id=*)
            ARTICLE_ID="${1#*=}"
            shift
            ;;
        --reset|-r)
            RESET_MODE=true
            shift
            ;;
        --help|-h)
            echo "Použití: $0 [OPTIONS] [ID_ČLÁNKU]"
            echo
            echo "Nástroj pro opravu migrace galerií v případě problémů s adresářovou strukturou"
            echo
            echo "Možnosti:"
            echo "  --id=NUMBER    ID článku pro opravu galerie (výchozí: 28931)"
            echo "  -r, --reset    Přepsat existující galerii, pokud existuje"
            echo "  -h, --help     Zobrazit tuto nápovědu"
            echo
            exit 0
            ;;
        *)
            # Pokud není rozpoznán formát --parametr=hodnota, zkusíme, jestli to není přímo ID
            if [[ "$1" =~ ^[0-9]+$ ]]; then
                ARTICLE_ID="$1"
            else
                echo "Neznámý parametr: $1"
                echo "Použijte '$0 --help' pro nápovědu."
            fi
            shift
            ;;
    esac
done

# Sestavení příkazu
CMD="./fix_gallery_28931.py $ARTICLE_ID"
if [ "$RESET_MODE" = true ]; then
    CMD="$CMD --reset"
    echo "Režim: Oprava galerie s resetem existující galerie"
else
    echo "Režim: Oprava galerie (bez resetu)"
fi

echo "Zpracovávám článek s ID: $ARTICLE_ID"
echo

echo "Spouštím: $CMD"
echo "============================================="

# Spuštění příkazu
$CMD

STATUS=$?
if [ $STATUS -eq 0 ]; then
    echo "============================================="
    echo "Oprava galerie pro článek ID $ARTICLE_ID dokončena úspěšně."
else
    echo "============================================="
    echo "CHYBA: Oprava galerie selhala s kódem $STATUS."
    echo "Zkontrolujte log soubory v adresáři logs/ pro více informací."
fi
