Metadata-Version: 2.0
Name: text-unidecode
Version: 1.3
Summary: The most basic Text::Unidecode port
Home-page: https://github.com/kmike/text-unidecode/
Author: <PERSON>
Author-email: <EMAIL>
License: Artistic License
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Artistic License
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: License :: OSI Approved :: GNU General Public License v2 or later (GPLv2+)
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Text Processing :: Linguistic

Text-Unidecode
==============

.. image:: https://travis-ci.org/kmike/text-unidecode.svg?branch=master
    :target: https://travis-ci.org/kmike/text-unidecode
    :alt: Build Status

text-unidecode is the most basic port of the
`Text::Unidecode <http://search.cpan.org/~sburke/Text-Unidecode-0.04/lib/Text/Unidecode.pm>`_
Perl library.

There are other Python ports of Text::Unidecode (unidecode_
and isounidecode_). unidecode_ is GPL; isounidecode_ uses too much memory,
and it didn't support Python 3 when this package was created.

You can redistribute it and/or modify this port under the terms of either:

* `Artistic License`_, or
* GPL or GPLv2+

If you're OK with GPL-only, use unidecode_ (it has better memory usage and
better transliteration quality).

``text-unidecode`` supports Python 2.7 and 3.4+.

.. _unidecode: https://pypi.python.org/pypi/Unidecode/
.. _isounidecode: https://pypi.python.org/pypi/isounidecode/
.. _Artistic License: https://opensource.org/licenses/Artistic-Perl-1.0

Installation
------------

::

    pip install text-unidecode

Usage
-----

::

    >>> from text_unidecode import unidecode
    >>> unidecode(u'какой-то текст')
    'kakoi-to tekst'


