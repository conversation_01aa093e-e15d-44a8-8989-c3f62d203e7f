#!/usr/bin/env python3
"""
Script pro opravu zbývajících starých URL obrázků v obsahu článků.
Používá opravenou funkci update_image_urls_in_content s podporou úvodního /.
"""

import logging
import mysql.connector
from db_connectors import get_mysql_connection
from config import TBL_WP_POSTS
from utils import load_mapping, update_image_urls_in_content

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fix_remaining_images(batch_size=50):
    """Opraví zbývající staré URL obrázků v obsahu článků"""
    logging.info("Spouštím opravu zbývajících obrázků v obsahu článků...")
    
    mysql_conn = get_mysql_connection()
    mysql_cursor = mysql_conn.cursor()

    # Načíst mapování
    image_map = load_mapping('image_map.json')
    logging.info(f"Načteno {len(image_map)} obrázků v mapování")

    # Počítadla
    updated_count = 0
    failed_count = 0

    try:
        # Najít všechny články se starými URL obrázků
        mysql_cursor.execute(f"""
            SELECT ID, post_content 
            FROM {TBL_WP_POSTS} 
            WHERE post_type = 'post' 
            AND post_status = 'publish'
            AND post_content LIKE '%/obrazek/%'
            ORDER BY ID
        """)
        
        articles_with_old_images = mysql_cursor.fetchall()
        total_articles = len(articles_with_old_images)
        logging.info(f"Nalezeno {total_articles} článků se starými URL obrázků")

        if total_articles == 0:
            logging.info("Žádné články se starými URL obrázků nenalezeny!")
            return

        # Zpracovat články po dávkách
        for i in range(0, total_articles, batch_size):
            batch = articles_with_old_images[i:i+batch_size]
            logging.info(f"Zpracovávám dávku {i+1}-{i+len(batch)} z {total_articles} článků")
            
            for wp_post_id, post_content in batch:
                try:
                    # Aktualizovat obrázky v obsahu
                    updated_content = update_image_urls_in_content(post_content, image_map)
                    
                    if updated_content != post_content:
                        # Aktualizovat obsah v databázi
                        mysql_cursor.execute(f"""
                            UPDATE {TBL_WP_POSTS} 
                            SET post_content = %s 
                            WHERE ID = %s
                        """, (updated_content, wp_post_id))
                        
                        updated_count += 1
                        logging.info(f"Aktualizován obsah článku ID {wp_post_id}")
                        
                        # Commit po každém článku pro bezpečnost
                        mysql_conn.commit()
                    else:
                        logging.debug(f"Článek ID {wp_post_id} - žádné změny")

                except Exception as e:
                    mysql_conn.rollback()
                    logging.error(f"Chyba při zpracování článku ID {wp_post_id}: {e}")
                    failed_count += 1

            logging.info(f"Dokončena dávka {i+1}-{i+len(batch)}")

    except Exception as e:
        logging.error(f"Obecná chyba: {e}")
    finally:
        mysql_cursor.close()
        mysql_conn.close()
        
        logging.info(f"Oprava dokončena:")
        logging.info(f"  - Aktualizováno: {updated_count} článků")
        logging.info(f"  - Selhalo: {failed_count} článků")

def test_image_replacement():
    """Test funkce nahrazování obrázků"""
    logging.info("Testování funkce nahrazování obrázků...")
    
    # Načíst mapování
    image_map = load_mapping('image_map.json')
    
    # Test obsah s různými variantami cest
    test_content = '''
    <p><img src="/obrazek/4ce13d224e0a1/1-resize_200x144.jpg" alt="test1"/></p>
    <p><img src="obrazek/4ce13d224e0a1/2-resize_200x145.jpg" alt="test2"/></p>
    <p><img src="/obrazek/680a1be023c59/2-nordic-(2)-1050x600-680a1f086e7e4.jpg" alt="test3"/></p>
    '''
    
    logging.info("Původní obsah:")
    logging.info(test_content)
    
    updated_content = update_image_urls_in_content(test_content, image_map)
    
    logging.info("Aktualizovaný obsah:")
    logging.info(updated_content)
    
    # Zkontrolovat, jestli se něco změnilo
    if updated_content != test_content:
        logging.info("✅ Funkce nahrazování funguje!")
    else:
        logging.warning("⚠️ Funkce nahrazování nefunguje - žádné změny")

if __name__ == "__main__":
    # Nejdříve otestovat funkci
    test_image_replacement()
    
    # Pak spustit opravu
    fix_remaining_images()
