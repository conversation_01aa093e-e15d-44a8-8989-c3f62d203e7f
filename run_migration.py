#!/usr/bin/env python3
import logging
import os
import sys
import time
import argparse
from datetime import datetime

# Nastavení logování
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Nastavení formátu logování
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

def check_dependencies():
    """Kontrola a instalace závislostí."""
    try:
        import psycopg2
        import mysql.connector
        from dotenv import load_dotenv
        from bs4 import BeautifulSoup
        from slugify import slugify
        from PIL import Image
        logging.info("Všechny závislosti jsou již nainstalovány.")
        return True
    except ImportError as e:
        logging.warning(f"Chybí některé z<PERSON>losti: {e}")
        logging.info("Instaluji chybějící závislosti...")
        
        try:
            import subprocess
            subprocess.check_call([
                sys.executable, "-m", "pip", "install",
                "psycopg2-binary", "mysql-connector-python", "python-dotenv",
                "Pillow", "python-slugify", "beautifulsoup4"
            ])
            logging.info("Závislosti úspěšně nainstalovány.")
            return True
        except Exception as e:
            logging.error(f"Chyba při instalaci závislostí: {e}")
            return False

def reset_database():
    """Resetuje WordPress databázi před migrací."""
    logging.info("=== RESET DATABÁZE ===")
    try:
        from reset_db import reset_database
        reset_database()
        return True
    except Exception as e:
        logging.error(f"Chyba při resetu databáze: {e}")
        return False

def run_migration(reset=False, only_images=False):
    """Spustí celý migrační proces."""
    start_time = time.time()
    
    logging.info("=== ZAČÁTEK MIGRACE ===")
    
    # 1. Kontrola závislostí
    if not check_dependencies():
        logging.error("Nelze pokračovat bez potřebných závislostí.")
        return False
    
    # 2. Import migračních modulů
    try:
        from migrate_categories import migrate_categories
        from migrate_users import migrate_users
        from migrate_images import migrate_images
        from migrate_articles import migrate_articles
        from migrate_galleries import migrate_galleries
    except ImportError as e:
        logging.error(f"Chyba při importu migračních modulů: {e}")
        return False
    
    # Reset databáze, pokud je požadován
    if reset:
        if not reset_database():
            logging.error("Reset databáze selhal, přerušuji migraci.")
            return False
    
    if only_images:
        # Spuštění pouze migrace obrázků
        logging.info("=== MIGRACE POUZE OBRÁZKŮ ===")
        try:
            migrate_images()
        except Exception as e:
            logging.error(f"Chyba při migraci obrázků: {e}")
            return False
    else:
        # 3. Spuštění migrace kategorií
        logging.info("=== MIGRACE KATEGORIÍ ===")
        try:
            migrate_categories()
        except Exception as e:
            logging.error(f"Chyba při migraci kategorií: {e}")
            logging.warning("Pokračuji s dalšími kroky...")
        
        # 4. Spuštění migrace uživatelů
        logging.info("=== MIGRACE UŽIVATELŮ ===")
        try:
            migrate_users()
        except Exception as e:
            logging.error(f"Chyba při migraci uživatelů: {e}")
            logging.warning("Pokračuji s dalšími kroky...")
        
        # 5. Spuštění migrace obrázků
        logging.info("=== MIGRACE OBRÁZKŮ ===")
        try:
            migrate_images()
        except Exception as e:
            logging.error(f"Chyba při migraci obrázků: {e}")
            logging.warning("Pokračuji s dalšími kroky...")
        
        # 6. Spuštění migrace článků
        logging.info("=== MIGRACE ČLÁNKŮ ===")
        try:
            migrate_articles()
        except Exception as e:
            logging.error(f"Chyba při migraci článků: {e}")
            logging.warning("Pokračuji s dalšími kroky...")
        
        # 7. Spuštění migrace galerií
        logging.info("=== MIGRACE GALERIÍ ===")
        try:
            migrate_galleries()
        except Exception as e:
            logging.error(f"Chyba při migraci galerií: {e}")
    
    # 8. Shrnutí
    end_time = time.time()
    duration = end_time - start_time
    hours, remainder = divmod(duration, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    logging.info("=== KONEC MIGRACE ===")
    logging.info(f"Celková doba trvání: {int(hours)}h {int(minutes)}m {int(seconds)}s")
    logging.info(f"Log soubor: {log_file}")
    
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Migrační skript pro přenos dat z PostgreSQL do WordPress')
    parser.add_argument('--reset', action='store_true', help='Resetovat WordPress databázi před migrací')
    parser.add_argument('--only-images', action='store_true', help='Spustit pouze migraci obrázků')
    args = parser.parse_args()
    
    success = run_migration(reset=args.reset, only_images=args.only_images)
    if success:
        print("\nMigrace dokončena. Pro detaily zkontrolujte log soubor.")
    else:
        print("\nMigrace selhala. Pro detaily zkontrolujte log soubor.")
